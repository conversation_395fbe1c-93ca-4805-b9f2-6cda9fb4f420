{"name": "vivya-crm", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.1", "@mui/material": "^6.4.1", "@mui/x-date-pickers": "^7.24.1", "@reduxjs/toolkit": "^2.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.8", "framer-motion": "^12.14.0", "material-react-table": "^3.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.1.2", "react-router-dom": "^7.0.2", "sass": "^1.81.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "vite": "^6.3.5"}}