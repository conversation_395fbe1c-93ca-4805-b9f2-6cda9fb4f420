import { lazy, Suspense } from "react";
import "./App.css";
import StoreProvider from "./store/storeProvider";
import { BrowserRouter as Router } from "react-router-dom";
import "./styles/main.scss";
import "react-loading-skeleton/dist/skeleton.css";
import CustomToast from "./components/shared/Components/CustomToast/CustomToast";
import { SkeletonTheme } from "react-loading-skeleton";
import FullPageLoader from "./components/shared/Loader/FullPageLoader/FullPageLoader";
const AppRouter = lazy(() => import("./routes/AppRouter"));

function App() {
  return (
    <StoreProvider>
      <SkeletonTheme
        baseColor="var(--color-secondary)"
        highlightColor="var(--color-background)"
      >
        <div className="App">
          <Router>
            <Suspense fallback={<FullPageLoader />}>
              <AppRouter />
            </Suspense>
          </Router>
        </div>
      </SkeletonTheme>
    </StoreProvider>
  );
}

export default App;
