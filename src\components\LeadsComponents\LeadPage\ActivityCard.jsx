import React from "react";
import ImportSvg from "../../shared/importsvg/ImportSvg";
import CompanyProfileImage from "../../registerCompanyComponents/CompanyProfileImage/CompanyProfileImage";

const ActivityCard = ({ leadData }) => {
  const [activeOption, setActiveOption] = React.useState("All");
  const activities = leadData?.activities;
  const ActivityItem = ({ activity }) => {
    return (
      <div className="lead-activity-card__item">
        <div className="lead-activity-card__item-header">
          <div className="lead-activity-card__item-image-container">
            <CompanyProfileImage companyName={activity.activity_by} />
          </div>
          <p>{activity.activity_name}</p>
        </div>
        <p className="lead-activity-card__item-details">
          <ImportSvg
            icon={"check-circle"}
            className="lead-activity-card__item-icon"
          />
          {activity.activity_details}
        </p>
        <div className="lead-activity-card__item-time">
          <p className="lead-activity-card__item-for">
            <ImportSvg
              icon={"calender"}
              className="lead-activity-card__item-icon"
            />
            {activity.activity_date}
          </p>
          <p className="lead-activity-card__item-by">
            <ImportSvg
              icon={"clock"}
              className="lead-activity-card__item-icon"
            />
            {activity.activity_time}
          </p>
        </div>
        <div className="lead-activity-card__item-time">
          <p className="lead-activity-card__item-for">
            <ImportSvg
              icon={"dollar-circle"}
              className="lead-activity-card__item-icon"
            />
            {activity.activity_for}
          </p>
          <p className="lead-activity-card__item-by">
            <ImportSvg
              icon={"company"}
              className="lead-activity-card__item-icon"
            />
            {activity.activity_by}
          </p>
        </div>
      </div>
    );
  };

  const options = [
    "All",
    "Activities",
    "Deals",
    "Notes",
    "Emails",
    "Files",
    "Texts",
    "Updates",
  ];

  return (
    <div className="lead-activity-card__content">
      <div className="lead-activity-card__header">
        <div className="lead-activity-card__menu">
          {options.map((option) => (
            <button
              key={option}
              className={`lead-activity-card__menu-item ${
                activeOption === option
                  ? "lead-activity-card__menu-item--active"
                  : ""
              }`}
              onClick={() => setActiveOption(option)}
            >
              {option}
            </button>
          ))}
        </div>
      </div>
      <div className="lead-activity-card__items">
        {activities?.map((activity, index) => (
          <ActivityItem key={`activity-${index}`} activity={activity} />
        ))}
      </div>
    </div>
  );
};

export default ActivityCard;
