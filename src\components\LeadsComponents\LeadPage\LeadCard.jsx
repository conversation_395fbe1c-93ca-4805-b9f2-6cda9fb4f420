import React from "react";
import CompanyProfileImage from "../../registerCompanyComponents/CompanyProfileImage/CompanyProfileImage";
import ImportSvg from "../../shared/importsvg/ImportSvg";
import ImportMenuSvg from "../../shared/sidebar/ImportmenuSvg";
import TextInput from "../../shared/Input Components/textinput/TextInput";

const LeadCard = ({ leadData }) => {
  const fields = leadData.data.map((section) => section.fields);
  const getFieldValue = (fieldName) =>
    fields.flatMap((section) =>
      section
        .filter((field) => field.name === fieldName)
        .map((field) => field.value)
    )[0];
  const firstName = getFieldValue("first_name");
  const lastName = getFieldValue("last_name");
  const email = getFieldValue("email");
  const phone = getFieldValue("phone");
  const [editMode, setEditMode] = React.useState({});
  const inputRefs = React.useRef({});
  const toggleEdit = (fieldId) => {
    setEditMode((prev) => {
      const newEditMode = { ...prev, [fieldId]: !prev[fieldId] };
      if (newEditMode[fieldId] && inputRefs.current[fieldId]) {
        setTimeout(() => {
          inputRefs.current[fieldId].focus();
        }, 0);
      }
      return newEditMode;
    });
  };
  console.log(editMode);

  return (
    <>
      <div className="lead-card">
        <div className="lead-card__header">
          <div className="lead-card__image-container">
            <CompanyProfileImage
              companyName={
                firstName || lastName ? `${firstName} ${lastName}` : ""
              }
            />
          </div>
          <div className="lead-card__name">
            <h3>{firstName || lastName ? `${firstName} ${lastName}` : ""}</h3>
            <p>{email}</p>
          </div>
        </div>
        <div className="lead-card__status">{leadData.current_stage}</div>
        <div className="lead-card__details">
          <div className="lead-card__details-item">
            <ImportSvg icon="owner" className="lead-card__details-icon" />
            <p>{email}</p>
          </div>
          <div className="lead-card__details-item">
            <ImportSvg icon="country" className="lead-card__details-icon" />
            <p>{phone}</p>
          </div>
        </div>
        <div className="lead-card__actions">
          <div className="lead-card__action-item">
            <ImportSvg icon="phone" className="lead-card__action-icon" />
          </div>
          <div className="lead-card__action-item">
            <ImportSvg icon="email" className="lead-card__action-icon" />
          </div>
          <div className="lead-card__action-item">
            <ImportSvg icon="note" className="lead-card__action-icon" />
          </div>
          <div className="lead-card__action-item">
            <ImportSvg icon="more" className="lead-card__action-icon" />
          </div>
        </div>
      </div>
      <div className="lead-card-form">
        <div className="lead-card-form__header">
          <h3>Lead Information</h3>
          <ImportMenuSvg icon="arrow" className="lead-card-form__edit-icon" />
        </div>
        <form className="lead-card-form__form">
          {leadData.data.map((section) => (
            <div key={section.section_name} className="lead-card-form__section">
              <h4>{section.section_name}</h4>
              <div className="lead-card-form__fields">
                {section.fields.map((field) => {
                  const isEditing = editMode[field.id] || false;
                  return (
                    <div className="editable-field" key={field.id}>
                      <TextInput
                        key={field.id}
                        label={field.label}
                        id={field.id}
                        value={field.value}
                        onChange={() => {}}
                        disabled={!isEditing}
                        inputRef={(el) => (inputRefs.current[field.id] = el)}
                      />
                      {isEditing ? (
                        <ImportSvg
                          icon="tick"
                          className="editable-field__svg"
                          onClick={() => toggleEdit(field.id)}
                        />
                      ) : (
                        <ImportSvg
                          icon="edit"
                          className="editable-field__svg"
                          onClick={() => toggleEdit(field.id)}
                        />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </form>
      </div>
    </>
  );
};

export default LeadCard;
