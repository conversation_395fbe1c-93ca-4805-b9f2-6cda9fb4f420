import React from "react";
import "./LeadPage.scss";
import CustomDropDown from "../../shared/Input Components/DropDown/CustomDropDown";
import SearchingInputComp from "../../shared/Input Components/SearchingInputComp/SearchingInputComp";
import LeadCard from "./LeadCard";
import OverviewCard from "./OverviewCard";
import ListingCard from "./ListingCard";
import ImportSvg from "../../shared/importsvg/ImportSvg";
import ActivityCard from "./ActivityCard";

const LeadPage = () => {
  const dummyData = {
    lead_id: "lead_001",
    current_stage: "Marketing Qualified Lead",
    overview: [
      {
        event_name: "Created At",
        date_time: "2023-01-01 10:00:00",
      },
      {
        event_name: "Last Communication Date",
        date_time: "2023-01-01 10:00:00",
      },
      {
        event_name: "Last Email Sent Date",
        date_time: "2023-01-01 10:00:00",
      },
      {
        event_name: "Last Call Date",
        date_time: "2023-01-01 10:00:00",
      },
    ],
    data: [
      {
        mandatory: true,
        column_type: "double",
        section_name: "Default",
        fields: [
          {
            id: "field_a4ww0e8jb",
            name: "first_name",
            type: "text",
            label: "First Name",
            default: true,
            required: true,
            selected: true,
            placeholder: "Enter first name",
            value: "Alice",
          },
          {
            id: "field_pajj1amwr",
            name: "last_name",
            type: "text",
            label: "Last Name",
            default: true,
            required: true,
            selected: true,
            placeholder: "Enter last name",
            value: "Smith",
          },
          {
            id: "field_naypt83q3",
            name: "email",
            type: "Email",
            label: "Email Address",
            default: true,
            required: true,
            selected: true,
            placeholder: "Enter email address",
            value: "<EMAIL>",
          },
          {
            id: "field_gxya2jcj8",
            name: "phone",
            type: "phone",
            label: "Phone Number",
            default: true,
            required: false,
            selected: true,
            placeholder: "Enter phone number",
            value: "************",
          },
        ],
      },
    ],
    deal: [
      {
        deal_id: "deal_001",
        deal_name: "Deal 1",
        deal_amount: "$ 10000",
        deal_stage: "stage_1",
        deal_close_date: "2023-01-01 10:00:00",
      },
      {
        deal_id: "deal_002",
        deal_name: "Deal 2",
        deal_amount: "$ 20000",
        deal_stage: "stage_2",
        deal_close_date: "2023-01-01 10:00:00",
      },
    ],
    files: [
      {
        file_id: "file_001",
        file_name: "File 1",
        file_type: "pdf",
        file_size: "1MB",
        file_created_at: "2023-01-01 10:00:00",
        file_url: "https://www.google.com",
      },
      {
        file_id: "file_002",
        file_name: "File 2",
        file_type: "pdf",
        file_size: "1MB",
        file_created_at: "2023-01-01 10:00:00",
        file_url: "https://www.google.com",
      },
    ],
    activities: [
      {
        activity_id: "activity_001",
        activity_name: "Task for you",
        activity_type: "tesk",
        activity_date: "2023-01-01",
        activity_time: "10:00:00",
        activity_details: "Follow up with Prasath",
        activity_for: "Vivya CRM - Sample Company",
        activity_by: "Prasath",
      },
      {
        activity_id: "activity_002",
        activity_name: "Email from Prasath",
        activity_type: "email",
        activity_date: "2023-01-01",
        activity_time: "10:00:00",
        activity_details: "Email from Prasath",
        activity_for: "Vivya CRM - Sample Company",
        activity_by: "Prasath",
      },
      {
        activity_id: "activity_003",
        activity_name: "Call from Prasath",
        activity_type: "call",
        activity_date: "2023-01-01",
        activity_time: "10:00:00",
        activity_details: "Call from Prasath",
        activity_for: "Vivya CRM - Sample Company",
        activity_by: "Prasath",
      },
      {
        activity_id: "activity_004",
        activity_name: "Note from Prasath",
        activity_type: "note",
        activity_date: "2023-01-01",
        activity_time: "10:00:00",
        activity_details: "Note from Prasath",
        activity_for: "Vivya CRM - Sample Company",
        activity_by: "Prasath",
      },
      {
        activity_id: "activity_005",
        activity_name: "Text from Prasath",
        activity_type: "text",
        activity_date: "2023-01-01",
        activity_time: "10:00:00",
        activity_details: "Text from Prasath",
        activity_for: "Vivya CRM - Sample Company",
        activity_by: "Prasath",
      },
      {
        activity_id: "activity_006",
        activity_name: "Update from Prasath",
        activity_type: "update",
        activity_date: "2023-01-01",
        activity_time: "10:00:00",
        activity_details: "Update from Prasath",
        activity_for: "Vivya CRM - Sample Company",
        activity_by: "Prasath",
      },
    ],
  };

  const deals = dummyData.deal.map((deal) => (
    <div key={deal.deal_id} className="lead-deal-card">
      <h3 className="lead-deal-card__name">{deal.deal_name}</h3>
      <p className="lead-deal-card__amount">
        {deal.deal_amount} . {deal.deal_close_date}
      </p>
      <div className="lead-deal-card__stage">{deal.deal_stage}</div>
    </div>
  ));

  const files = dummyData.files.map((file) => (
    <div key={file.file_id} className="lead-file-card">
      <div className="lead-file-card__icon-cont">
        <ImportSvg icon="file" className="lead-file-card__icon" />
      </div>
      <div className="lead-file-card__content">
        <h3 className="lead-file-card__name">{file.file_name}</h3>
        <div className="lead-file-card__details">
          <p className="lead-file-card__created">{file.file_created_at}</p>
          <p className="lead-file-card__size">{file.file_size}</p>
        </div>
      </div>
      <div className="lead-file-card__actions">
        <ImportSvg icon="more" className="lead-file-card__action-icon" />
      </div>
    </div>
  ));

  return (
    <div className="lead-page">
      <div className="lead-page__header">
        <CustomDropDown variant="small" />
        <div className="lead-page__header-search">
          <SearchingInputComp />
          <button className="btnbtn__primary lead-page__header-btn">New</button>
        </div>
      </div>
      <main className="lead-page__main">
        <section className="lead-page__main-left">
          <LeadCard leadData={dummyData} />
        </section>
        <section className="lead-page__content">
          <ActivityCard leadData={dummyData} />
        </section>
        <section className="lead-page__main-right">
          <OverviewCard leadData={dummyData} />
          <ListingCard title={"Deal"}>{deals}</ListingCard>
          <ListingCard title={"Files"}>{files}</ListingCard>
        </section>
      </main>
    </div>
  );
};

export default LeadPage;
