.lead-page {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  overflow: auto;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__header-search {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  &__header-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
  }
  &__main {
    display: flex;
    flex: 1;
    gap: 1rem;
    overflow: auto;
  }
  &__main-left {
    flex: 0.3;
    border-radius: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: auto;
  }
  &__content {
    flex: 0.7;
    border-radius: 1rem;
    overflow: auto;
    border: 1px solid var(--color-stroke);
    padding: 0.5rem;
    overflow: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  &__main-right {
    flex: 0.3;
    border-radius: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.lead-card {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  &__header {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  &__image-container {
    width: 5rem;
    height: 5rem;
    font-size: 3rem;
  }
  &__status {
    background-color: var(--color-red-secondary);
    width: max-content;
    padding: 0.2rem 1.5rem;
    border-radius: 2rem;
  }
  &__details {
    display: flex;
    gap: 0.5rem;
    & * {
      width: 50%;
    }
  }
  &__details-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    p {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }
  }
  &__details-icon {
    width: 2rem;
    height: 2rem;
    color: var(--color-primary);
  }
  &__actions {
    display: flex;
    justify-content: center;
    flex: 1;
    gap: 1.5rem;
  }
  &__action-item {
    display: flex;
    align-items: center;
    padding: 0.8rem;
    gap: 0.5rem;
    background-color: var(--color-secondary);
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-primary);
    &:hover {
      color: var(--color-primary-darker);
    }
  }
  &__action-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
}

.lead-card-form {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  overflow: auto;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__edit-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
    cursor: pointer;
    &:hover {
      color: var(--color-primary-darker);
    }
  }
  &__form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 1;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  &__section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  &__fields {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 1.5rem;
  }
}

.lead-overview-card {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__edit-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
    cursor: pointer;
    &:hover {
      color: var(--color-primary-darker);
    }
  }
  &__content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  &__item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.5rem;
    border-radius: 0.5rem;
    border: 1px solid var(--color-stroke);
    gap: 0.5rem;
  }

  &__item-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  &__item-time {
    font-weight: 200;
  }
  &__icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
  }
}

.lead-listing-card {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  &__add-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    color: var(--color-primary);
    cursor: pointer;
  }
  &__add-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
  }
  &__icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
    cursor: pointer;
    &:hover {
      color: var(--color-primary-darker);
    }
  }
  &__content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow: auto;
  }
}

.lead-deal-card {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  &__name {
    font-size: 1.5rem;
    font-weight: 600;
  }
  &__amount {
    font-weight: 200;
  }
  &__stage {
    padding: 0.5rem 2rem;
    border-radius: 1.5rem;
    background-color: var(--color-yellow-secondary);
    width: max-content;
  }
}

.lead-file-card {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  &__icon-cont {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 5rem;
    height: 5rem;
    border-radius: 0.5rem;
  }
  &__icon {
    width: 3rem;
    height: 3rem;
    color: var(--color-primary);
  }
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  &__name {
    font-size: 1.5rem;
    font-weight: 600;
  }
  &__details {
    display: flex;
    gap: 2rem;
    & * {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  &__size {
    font-weight: 200;
  }
  &__created {
    font-weight: 200;
  }
  &__actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  &__action-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
    cursor: pointer;
    &:hover {
      color: var(--color-primary-darker);
    }
  }
}

.lead-activity-card {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  &__content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    overflow: auto;
  }
  &__header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  &__menu {
    display: flex;
    gap: 0.5rem;
  }
  &__menu-item {
    padding: 0.3rem 1rem;
    border-radius: 0.5rem;
    color: var(--color-text);
    border: 1px solid var(--color-stroke);
    cursor: pointer;
    &:hover {
      background-color: var(--color-secondary);
    }
    &--active {
      background-color: var(--color-secondary);
      border: 1px solid var(--color-primary);
    }
  }
  &__items {
    display: flex;
    flex-direction: column;
    padding: 0.5rem 1rem;
    gap: 0.5rem;
    flex: 1;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  &__item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--color-stroke);
    background-color: var(--color-extreme-2);
  }
  &__item-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  &__item-image-container {
    width: 4rem;
    height: 4rem;
    font-size: 2rem;
  }
  &__item-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
  }
  &__item-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    padding: 0.5rem;
  }
  &__item-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0 0.5rem;
  }
  &__item-for {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
  }
  &__item-by {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
  }
}

.editable-field {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  &__svg {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
    position: absolute;
    right: 1rem;
    cursor: pointer;
    &:hover {
      color: var(--color-primary-darker);
    }
  }
}
