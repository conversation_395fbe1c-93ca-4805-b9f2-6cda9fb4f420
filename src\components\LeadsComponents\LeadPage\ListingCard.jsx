import React from "react";
import ImportMenuSvg from "../../shared/sidebar/ImportmenuSvg";
import ImportSvg from "../../shared/importsvg/ImportSvg";

const ListingCard = ({
  title,
  btnName = "Add",
  btnIcon = "add-new",
  children,
}) => {
  return (
    <div className="lead-listing-card">
      <div className="lead-listing-card__header">
        <h3>{title}</h3>
        <div className="lead-listing-card__actions">
          <button className="btn lead-listing-card__add-btn">
            <ImportSvg icon={btnIcon} className="lead-listing-card__add-icon" />
            {btnName}
          </button>
          <ImportMenuSvg icon="arrow" className="lead-listing-card__icon" />
        </div>
      </div>
      <div className="lead-listing-card__content">{children}</div>
    </div>
  );
};

export default ListingCard;
