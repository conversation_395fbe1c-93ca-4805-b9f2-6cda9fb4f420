import React from "react";
import ImportMenuSvg from "../../shared/sidebar/ImportmenuSvg";
import ImportSvg from "../../shared/importsvg/ImportSvg";

const OverviewCard = ({ leadData }) => {
  const overviews = leadData.overview;
  const OverviewItem = ({ overview }) => {
    return (
      <div className="lead-overview-card__item">
        <div className="lead-overview-card__item-header">
          <ImportSvg icon="update" className="lead-overview-card__icon" />
          <p>{overview.event_name}</p>
        </div>
        <p className="lead-overview-card__item-time">{overview.date_time}</p>
      </div>
    );
  };

  return (
    <div className="lead-overview-card">
      <div className="lead-overview-card__header">
        <h3>Overview</h3>
        <ImportMenuSvg icon="arrow" className="lead-overview-card__edit-icon" />
      </div>
      <div className="lead-overview-card__content">
        {overviews?.map((overview, index) => (
          <OverviewItem key={`overview-${index}`} overview={overview} />
        ))}
      </div>
    </div>
  );
};

export default OverviewCard;
