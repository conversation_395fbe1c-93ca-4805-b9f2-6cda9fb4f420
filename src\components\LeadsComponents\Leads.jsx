import React from "react";
import "./Leads.scss";
import ImportSvg from "../shared/importsvg/ImportSvg";
import SearchingInputComp from "../shared/Input Components/SearchingInputComp/SearchingInputComp";
import { useSelector } from "react-redux";
import DynamicForm from "../shared/DynamicForm/DynamicForm";
import useFetchReferenceFields from "../formConfiguration/useFetchReferenceFields";
import useTableColumns from "../../utilities/constants/useTableColumns";
import useDynamicTable from "../../utilities/hooks/useDynamicTable";
import useFormData from "../../utilities/hooks/useFormData";
import { payload } from "../../utilities/payload";
import useTriggerApiWithQueryParams from "../../utilities/hooks/useTriggerApiWithQueryParams";
import { leadsList } from "../../store/actions/leadsAction";
import CustomTopBar from "../shared/Components/DynamicTable/CustomTopBar";
import { MRT_TableContainer } from "material-react-table";

const Leads = () => {
  const [addLead, setAddLead] = React.useState(false);
  const formFields = useSelector(
    (state) => state.formConfigure.referenceFields
  );
  const leads = useSelector((state) => state.leads.leadsList);
  const { formData, setFormData } = useFormData(payload.leadsList);
  const relevantFields = formFields?.data?.lead_fields;
  useFetchReferenceFields();
  const { errorOccured } = useTriggerApiWithQueryParams(
    leadsList,
    formData,
    setFormData,
    leads?.isLoading
  );
  const scrollRef = React.useRef();
  const convertedData = React.useMemo(() => {
    return (
      leads?.data?.map((eachLead) => {
        const leadParse = JSON.parse(eachLead.lead_fields);
        const flattenedFields = leadParse.flatMap(
          (section) => section?.fields || []
        );
        return flattenedFields.reduce((acc, field) => {
          acc[field.id] = field.value;
          return acc;
        }, {});
      }) || []
    );
  }, [leads?.data]);
  const { LeadsTableColumns } = useTableColumns();
  const { table } = useDynamicTable({
    columns: LeadsTableColumns || [],
    tableData: {
      response_data: convertedData,
      next_page: leads?.data?.next_page,
    },
    loading: leads?.isLoading,
    setFormData,
    errorOccured,
    scrollRef,
  });

  return (
    <div className="leads">
      <CustomTopBar table={table} ignoreViewType>
        <div className="leads__header">
          <h1></h1>
          <div className="leads__header-buttons">
            <button className="btn" onClick={() => setAddLead(true)}>
              <ImportSvg icon="add-new" className="leads__header-icon" />
              New
            </button>
          </div>
        </div>
      </CustomTopBar>
      <MRT_TableContainer table={table} />
      {relevantFields && (
        <DynamicForm
          displayForm={addLead}
          closeForm={() => setAddLead(false)}
          formType="lead"
          relevantFields={relevantFields}
        />
      )}
    </div>
  );
};

export default Leads;
