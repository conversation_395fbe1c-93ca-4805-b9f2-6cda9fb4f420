.leads {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  overflow: auto;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__header-buttons {
    display: flex;
    gap: 1rem;
    .btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      border-radius: 0.5rem;
      color: var(--color-text);
      background-color: var(--color-extreme-2);
    }
  }
  &__header-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
  }
}
