import React from "react";
import "../../rolesComponents/ListTableComponent/ListTableComponent.scss";
import CustomCheckBox from "../../shared/Input Components/CustomChechBox/CustomCheckBox";
import ImportSvg from "../../shared/importsvg/ImportSvg";
import TableActionField from "../../shared/Components/TableActionField/TableActionField";
import StatusBarComponent from "../../shared/Components/StatusBar/StatusBarComponent";

function UserTableComponent() {
  const tableHeadings = [
    "#ID",
    "Name",
    "Email",
    "Role",
    "Last Login",
    "Status",
    "Active",
  ];
  const tableValue = [
    "81283",
    "Vignesh",
    "<EMAIL>",
    "User",
    "12/12/22",
  ];
  const handleView = () => {};
  const handleClose = () => {};
  const handleEdit = () => {};
  return (
    <section className="list-table">
      <div className="list-table__header">
        <div className="list-table__row-data">
          <CustomCheckBox size="small" />
        </div>
        {tableHeadings.map((value, index) => (
          <div key={index} className="list-table__heading">
            <ImportSvg
              icon="draggable-icon"
              className="list-table__draggable-svg"
            />
            <p className="list-table__heading-topic">{value}</p>
          </div>
        ))}
      </div>
      {Array.from({ length: 40 }).map((_, idx) => (
        <div key={idx} className="list-table__row">
          <div className="list-table__row-data">
            <CustomCheckBox size="small" />
          </div>
          {tableValue.map((value, index) => (
            <p key={index} className="list-table__row-data">
              {value}
            </p>
          ))}
          <div className="list-table__status-row">
            <StatusBarComponent status="deactivated" />
          </div>
          <TableActionField
            handleClose={handleClose}
            handleView={handleView}
            handleEdit={handleEdit}
          />
        </div>
      ))}
    </section>
  );
}

export default UserTableComponent;
