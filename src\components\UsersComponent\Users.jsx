import { useMemo, useRef, useState } from "react";
import "./Users.scss";
import ImportSvg from "../shared/importsvg/ImportSvg";
import DynamicForm from "../shared/DynamicForm/DynamicForm";
import { useSelector } from "react-redux";
import useFetchReferenceFields from "../formConfiguration/useFetchReferenceFields";
import useFormData from "../../utilities/hooks/useFormData";
import { payload } from "../../utilities/payload";
import useTriggerApiWithQueryParams from "../../utilities/hooks/useTriggerApiWithQueryParams";
import { allCompanyUsers } from "../../store/actions/usersAction";
import useTableColumns from "../../utilities/constants/useTableColumns";
import useDynamicTable from "../../utilities/hooks/useDynamicTable";
import CustomTopBar from "../shared/Components/DynamicTable/CustomTopBar";
import {
  MRT_LinearProgressBar,
  MRT_TableContainer,
} from "material-react-table";

function Users() {
  // const count = 22;
  // const [activeFilter, setActiveFilter] = useState("all");
  // const [selectedTypeofUser, setSelectedTypeOfUser] = useState("All Users");
  // const TypesOfUser = [
  //   "All Users",
  //   "Active Users",
  //   "Pending Users",
  //   "Deactivated Users",
  //   "Deleted Users",
  // ];
  // const UsersCount = [22, 10, 5, 2, 1];
  const [addUser, setAdduser] = useState(false);
  const formFields = useSelector(
    (state) => state.formConfigure.referenceFields
  );
  const user = useSelector((state) => state.user.allCompanyUsers);
  const scrollRef = useRef();
  useFetchReferenceFields();
  const relevantFields = formFields?.data?.user_fields;
  const { formData, setFormData } = useFormData(payload.allCompanyUsers);
  const { errorOccured } = useTriggerApiWithQueryParams(
    allCompanyUsers,
    formData,
    setFormData,
    user?.isLoading
  );
  const { UsersTableColumns } = useTableColumns();
  const convertedData = useMemo(() => {
    return (
      user?.data?.response_data?.map((eachUser) => {
        const flattenedFields = eachUser.flatMap(
          (section) => section?.fields || []
        );
        return flattenedFields.reduce((acc, field) => {
          acc[field.id] = field.value;
          return acc;
        }, {});
      }) || []
    );
  }, [user?.data]);

  const { table } = useDynamicTable({
    columns: UsersTableColumns || [],
    tableData: {
      response_data: convertedData,
      next_page: user?.data?.next_page,
    },
    loading: user?.isLoading,
    setFormData,
    errorOccured,
    scrollRef,
  });
  return (
    <>
      <div className="users">
        <CustomTopBar table={table} ignoreViewType>
          <div className="users__add-new-container">
            <button
              onClick={() => setAdduser(true)}
              className="btn btn__primary users__add-btn"
            >
              <ImportSvg icon="add-new" className="users__add-new-svg" />
              New User
            </button>
          </div>
        </CustomTopBar>
        <MRT_LinearProgressBar table={table} />
        <MRT_TableContainer table={table} />
      </div>
      {relevantFields && (
        <section className="dynamic-table">
          <DynamicForm
            displayForm={addUser}
            closeForm={() => setAdduser(false)}
            formType="user"
            relevantFields={relevantFields}
          />
        </section>
      )}
    </>
  );
}

export default Users;
