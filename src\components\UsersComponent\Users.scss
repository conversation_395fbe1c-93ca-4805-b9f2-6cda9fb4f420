.users {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  padding: 2rem 0rem;
  height: 100%;
  position: relative;
  &__header {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }
  &__filter-container {
    max-width: 80rem;
    min-width: 60rem;
  }
  &__add-new-container {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  &__select-dropdown {
    border-radius: 0.5rem;
    // min-width: 10rem;
    // max-width: 20rem;
  }
  &__add-new-svg {
    width: 1.5rem;
    height: 1.5rem;
  }
  &__add-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-radius: 0.3rem;
    white-space: nowrap;
  }
  &__filter {
    display: flex;
    padding: 0.3rem 0.5rem;
    align-items: center;
    width: max-content;
    border: 1px solid transparent;
    gap: 1rem;
    cursor: pointer;
    &--active {
      background-color: var(--color-secondary);
      border: 1px solid var(--color-primary);
      border-radius: 0.3rem;
      & > *:nth-child(2) {
        background-color: var(--color-extreme-2);
        color: var(--color-text);
      }
    }
  }
  &__filter-name {
    font-size: 1.1rem;
    color: var(--color-text);
  }
  &__filter-count {
    border-radius: 0.2rem;
    background-color: var(--color-secondary);
    font-size: 1rem;
    padding: 0.2rem 0.4rem;
    color: var(--color-primary);
  }
}
