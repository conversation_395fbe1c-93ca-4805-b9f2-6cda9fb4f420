.grid-view-comp {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  gap: 2rem;
  row-gap: 2rem;
  &::-webkit-scrollbar {
    display: none;
  }
  & > * {
    // flex: 1 1;
    max-width: 30rem;
    // flex: 0 1 30rem;
    // max-width: 100%;
    // box-sizing: border-box;
  }
  &__lesser-count {
    // justify-content: normal;
    padding: 1rem 5rem;
    // display: flex;
    // flex-wrap: wrap;
    width: 100%;
    display: grid;
    height: min-content;
    grid-template-columns: repeat(auto-fill, minmax(28rem, 1fr));
    row-gap: 2rem;
    column-gap: 2rem;
    &--active {
      height: 100%;
    }
  }
  &__lesser-count > * {
    max-width: none;
  }
}

@keyframes stretch {
  0% {
    row-gap: 2rem;
    column-gap: 2rem;
  }
  50% {
    row-gap: 2.5rem;
    column-gap: 2.5rem;
  }
  100% {
    row-gap: 2rem;
    column-gap: 2rem;
  }
}
