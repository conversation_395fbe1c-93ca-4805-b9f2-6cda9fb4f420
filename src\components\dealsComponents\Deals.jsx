import React from "react";
import "./Deals.scss";
import { useSelector } from "react-redux";
import useFetchReferenceFields from "../formConfiguration/useFetchReferenceFields";
import DynamicForm from "../shared/DynamicForm/DynamicForm";
import LeadsBoard from "./DealsBoard";
import { leadsData as initialLeadsData } from "./LeadsData";
import ModalWrapper from "../shared/modalComponents/ModalWrapper/ModalWrapper";
import TextInput from "../shared/Input Components/textinput/TextInput";
import useFormData from "../../utilities/hooks/useFormData";

const Deals = () => {
  const [addLead, setAddLead] = React.useState(false);
  const [addStage, setAddStage] = React.useState(false);
  const formFields = useSelector(
    (state) => state.formConfigure.referenceFields
  );
  const [stages, setStages] = React.useState(initialLeadsData);
  const initialAddStageData = {
    stage_name: "",
    stage_id: "",
    leads: [],
  };
  const { formData, setFormData, handleChange } =
    useFormData(initialAddStageData);

  useFetchReferenceFields();

  const relevantFields = formFields?.data?.lead_fields;

  const handleCloseAddStage = () => {
    setAddStage(false);
    setFormData(initialAddStageData);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setStages([
      ...stages,
      {
        ...formData,
        stage_id: `stage_${stages.length + 1}`,
        leads: [],
      },
    ]);
    setAddStage(false);
    setFormData(initialAddStageData);
  };
  return (
    <div className="leads">
      <div className="leads__header">
        <h1>Deals</h1>
        <div className="leads__header-buttons">
          <button
            className="btn btn__primary btn__elevated"
            onClick={() => setAddStage(true)}
          >
            Add Stage
          </button>
          <button
            className="btn btn__primary btn__elevated"
            onClick={() => setAddLead(true)}
          >
            Add Deal/Lead
          </button>
        </div>
      </div>
      <LeadsBoard stages={stages} setStages={setStages} />
      {relevantFields && (
        <DynamicForm
          displayForm={addLead}
          closeForm={() => setAddLead(false)}
          formType="lead"
          relevantFields={relevantFields}
        />
      )}

      <ModalWrapper
        displayModal={addStage}
        closeForm={handleCloseAddStage}
        formType="stage"
      >
        <div className="leads__add-stage">
          <h1>Add Stage</h1>
          <form className="leads__add-stage-form" onSubmit={handleSubmit}>
            <TextInput
              label="Stage Name"
              name="stageName"
              type="text"
              placeholder="Enter Stage Name"
              value={formData.stage_name}
              id="stage_name"
              onChange={handleChange}
            />
            <div className="leads__add-stage-form-buttons">
              <button
                className="btn btn__secondary"
                onClick={handleCloseAddStage}
                type="button"
              >
                Cancel
              </button>
              <button className="btn btn__primary" type="submit">
                Add Stage
              </button>
            </div>
          </form>
        </div>
      </ModalWrapper>
    </div>
  );
};

export default Deals;
