.leads {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 1rem 0;
  overflow: auto;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
  }
  &__header-buttons {
    display: flex;
    gap: 1rem;
    .btn {
      border-radius: 0.5rem;
    }
  }
  &__add-stage {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
    min-width: 30rem;
    color: var(--color-text);
  }
  &__add-stage-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    padding: 0 2rem;
  }
  &__add-stage-form-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
    .btn {
      border-radius: 0.5rem;
    }
  }
}
