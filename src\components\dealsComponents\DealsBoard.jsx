import React from "react";
import "./DealsBoard.scss";
import { Reorder } from "framer-motion";
import StageItem from "./StageItem";

const DealsBoard = ({ stages, setStages }) => {
  const [draggedLead, setDraggedLead] = React.useState(null);
  const [sourceStageId, setSourceStageId] = React.useState(null);
  const isDropHandledRef = React.useRef(false);

  const handleDrop = (targetStageId, targetLeadId = null) => {
    if (isDropHandledRef.current) return;
    isDropHandledRef.current = true;
    console.log("handleDrop", targetStageId, targetLeadId);
    if (!draggedLead || !sourceStageId) return;

    setStages((prevStages) => {
      return prevStages.map((stage) => {
        if (
          stage.stage_id === sourceStageId &&
          stage.stage_id === targetStageId
        ) {
          // Dragging within the same stage
          const filteredLeads = stage.leads.filter(
            (lead) => lead.lead_id !== draggedLead.lead_id
          );

          let insertIndex = targetLeadId
            ? filteredLeads.findIndex((l) => l.lead_id === targetLeadId)
            : filteredLeads.length;

          if (insertIndex === -1) insertIndex = filteredLeads.length;

          const newLeads = [...filteredLeads];
          newLeads.splice(insertIndex, 0, draggedLead);

          return {
            ...stage,
            leads: newLeads,
          };
        }

        if (stage.stage_id === sourceStageId) {
          // Remove from source stage
          return {
            ...stage,
            leads: stage.leads.filter(
              (lead) => lead.lead_id !== draggedLead.lead_id
            ),
          };
        }

        if (stage.stage_id === targetStageId) {
          // Insert into target stage
          let insertIndex = targetLeadId
            ? stage.leads.findIndex((l) => l.lead_id === targetLeadId)
            : stage.leads.length;

          if (insertIndex === -1) insertIndex = stage.leads.length;

          const newLeads = [...stage.leads];
          newLeads.splice(insertIndex, 0, draggedLead);

          return {
            ...stage,
            leads: newLeads,
          };
        }

        return stage; // all other stages unchanged
      });
    });

    setDraggedLead(null);
    setSourceStageId(null);
    setTimeout(() => {
      isDropHandledRef.current = false;
    }, 0);
  };

  return (
    <Reorder.Group
      axis="x"
      values={stages.map((s) => s.stage_id)} // use IDs
      onReorder={(newOrder) => {
        const reorderedStages = newOrder
          .map((id) => {
            const match = stages.find((s) => s.stage_id === id);
            return match ? JSON.parse(JSON.stringify(match)) : null;
          })
          .filter(Boolean);
        setStages(reorderedStages);
      }}
      className="leads-board"
    >
      {stages.map((stage) => (
        <StageItem
          key={stage.stage_id}
          stage={stage}
          stageId={stage.stage_id}
          setDraggedLead={setDraggedLead}
          setSourceStageId={setSourceStageId}
          handleDrop={handleDrop}
          draggedLead={draggedLead}
        />
      ))}
    </Reorder.Group>
  );
};

export default DealsBoard;
