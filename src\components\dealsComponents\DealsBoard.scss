.leads-board {
  display: flex;
  flex: 1;
  gap: 2.5rem;
  padding: 1rem;
  overflow: auto;
  &__stage {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-primary-card);
    min-width: 30rem;
  }
  &__stage-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    gap: 0.5rem;
    background-color: var(--color-extreme-2);
    box-shadow: var(--shadow);
    border-radius: 0.2rem;
  }
  &__stage-card-icon {
    width: 2rem;
    height: 2rem;
    color: var(--color-secondary);
    scale: 1.2;
    cursor: grab;
    &:active {
      cursor: grabbing;
    }
    &:hover {
      color: var(--color-primary);
    }
  }
  &__stage-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    width: 100%;
    white-space: nowrap;
  }
}

.leads-card {
  display: flex;
  background-color: var(--color-extreme-2);
  border-radius: 0.5rem;
  padding: 1rem;
  gap: 1rem;
  box-shadow: var(--shadow);
  cursor: grab;
  transition: all 0.5s ease;
  margin: 0 0.8rem;
  &:active {
    cursor: grabbing;
  }
  &.dragged {
    transform: scale(1.05);
    box-shadow: var(--shadow-2);
  }
  &__image-container {
    width: 5rem;
    height: 5rem;
    font-size: 3rem;
  }
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    h3 {
      font-size: 1.5rem;
      font-weight: 600;
    }
  }
  &__content-container {
    display: flex;
    gap: 0.5rem;
    flex: 1;
  }

  &__container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
  }
}
