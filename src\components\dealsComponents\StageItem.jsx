import React from "react";
import { Reorder, useDragControls } from "framer-motion";
import ImportSvg from "../shared/importsvg/ImportSvg";
import CompanyProfileImage from "../registerCompanyComponents/CompanyProfileImage/CompanyProfileImage";

const StageItem = React.memo(
  ({
    stageId,
    stage,
    setDraggedLead,
    setSourceStageId,
    handleDrop,
    draggedLead,
  }) => {
    const controls = useDragControls();

    const handleDragOver = (e) => {
      e.preventDefault(); // Required to allow dropping
    };

    const handleDropOnStage = (e) => {
      e.preventDefault();
      e.stopPropagation();
      handleDrop(stageId);
    };

    return (
      <Reorder.Item
        as="div"
        value={stage?.stage_id}
        id={stage?.stage_id}
        className="leads-board__stage"
        dragListener={false}
        dragControls={controls}
        drag="x"
        onDragOver={handleDragOver}
        onDrop={handleDropOnStage}
      >
        <div className="leads-board__stage-card">
          <ImportSvg
            icon="draggable-icon"
            className="leads-board__stage-card-icon"
            onPointerDown={(e) => {
              e.preventDefault();
              controls.start(e);
            }}
          />
          <h2 className="leads-board__stage-card-title">{stage?.stage_name}</h2>
          <ImportSvg
            icon="expand-more"
            className="leads-board__stage-card-icon"
          />
        </div>

        <div className="leads-card__container">
          {stage?.leads?.map((lead) => {
            const isDragged =
              draggedLead && draggedLead.lead_id === lead.lead_id;
            return (
              <div
                className={`leads-card${isDragged ? " dragged" : ""}`}
                key={lead?.lead_id}
                draggable
                onDragStart={() => {
                  setDraggedLead(lead);
                  setSourceStageId(stageId);
                }}
                onDragEnd={() => {
                  setDraggedLead(null);
                  setSourceStageId(null);
                }}
                onDragOver={(e) => {
                  e.preventDefault();
                }}
                onDrop={() => {
                  handleDrop(stageId, lead.lead_id);
                }}
              >
                <div className="leads-card__image-container">
                  <CompanyProfileImage
                    imageUrl={
                      lead?.fields.find((f) => f.name === "image")?.value
                    }
                    companyName={
                      lead?.fields?.find((f) => f.name === "first_name")?.value
                    }
                  />
                </div>
                <div className="leads-card__content">
                  <h3>
                    {lead.fields.find((f) => f.name === "first_name")?.value}
                  </h3>
                  <p>{lead.fields.find((f) => f.name === "email")?.value}</p>
                </div>
              </div>
            );
          })}
        </div>
      </Reorder.Item>
    );
  }
);

export default StageItem;
