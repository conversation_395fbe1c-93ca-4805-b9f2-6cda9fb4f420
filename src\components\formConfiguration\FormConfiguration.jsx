import React, { useEffect, useRef } from "react";
import "./FormConfiguration.scss";
import ImportSvg from "../shared/importsvg/ImportSvg";
import InfoHeader from "./InfoHeader";
import ModalWrapper from "../shared/modalComponents/ModalWrapper/ModalWrapper";
import AddNewSection from "./AddNewSection";
import AddNewField from "./AddNewField/AddNewField";
import SlidingFormWrapper from "../shared/modalComponents/SlidingFormWrapper/SlidingFormWrapper";
import ToggleButton from "../shared/Input Components/ToogleButton/ToggleButton";
import CustomDropDown from "../shared/Input Components/DropDown/CustomDropDown";
import { useSelector } from "react-redux";
import TextInput from "../shared/Input Components/textinput/TextInput";
import ColumnView from "../shared/Components/ToggleView/ColumnView";
import useFetchReferenceFields from "./useFetchReferenceFields";
import { Reorder } from "framer-motion";
import useApiHandler from "../../utilities/hooks/useApiHandler";
import { createReferenceFields } from "../../store/actions/fromConfigureAction";
import { useToast } from "../../utilities/hooks/useToast";

const FormConfiguration = () => {
  const [showPreview, setShowPreview] = React.useState(false);
  const [addSection, setAddSection] = React.useState(false);
  const [showAddField, setShowAddField] = React.useState(false);
  const [formType, setFormType] = React.useState("Users");
  const [count, setCount] = React.useState({
    available: 0,
    selected: 0,
  });
  const [selectedFormFields, setSelectedFormFields] = React.useState([]);
  const [temproaryFormFields, setTemproaryFormFields] = React.useState([]);
  const formFields = useSelector(
    (state) => state.formConfigure.referenceFields
  );
  useFetchReferenceFields();
  useEffect(() => {
    setSelectedFormFields(
      formType === "Users"
        ? formFields?.data?.user_fields
        : formFields?.data?.lead_fields
    );
    setTemproaryFormFields(
      formType === "Users"
        ? formFields?.data?.user_fields
        : formFields?.data?.lead_fields
    );
  }, [formFields, formType]);

  useEffect(() => {
    setCount({
      available: selectedFormFields?.reduce(
        (acc, section) => acc + (section.fields?.length || 0),
        0
      ),
      selected: selectedFormFields?.reduce(
        (acc, section) =>
          acc + (section.fields?.filter((field) => field.selected).length || 0),
        0
      ),
    });
  }, [formFields, selectedFormFields]);

  const generateId = () => `field_${Math.random().toString(36).substr(2, 9)}`;

  useEffect(() => {
    setTemproaryFormFields((prevFields) => {
      const updated = prevFields?.map((section) => {
        const updatedFields = section.fields?.map((field) =>
          field.id ? field : { ...field, id: generateId() }
        );
        return { ...section, fields: updatedFields };
      });
      return updated;
    });
  }, [formFields, formType]);

  const handleReorder = (sectionIndex, newFieldIds) => {
    setTemproaryFormFields((prevFields) =>
      prevFields.map((section, i) => {
        if (i !== sectionIndex) return section;

        const selectedFields = section.fields.filter((f) => f.selected);
        const unselectedFields = section.fields.filter((f) => !f.selected);

        const idToFieldMap = Object.fromEntries(
          selectedFields.map((f) => [f.id, f])
        );
        const reorderedSelectedFields = newFieldIds.map(
          (id) => idToFieldMap[id]
        );

        // Rebuild the full list: replace selected fields in their original order positions
        const fullReorderedFields = [];
        let selectedIndex = 0;

        for (const field of section.fields) {
          if (field.selected) {
            fullReorderedFields.push(reorderedSelectedFields[selectedIndex++]);
          } else {
            fullReorderedFields.push(field);
          }
        }

        return { ...section, fields: fullReorderedFields };
      })
    );
  };

  const callApi = useApiHandler();
  const toast = useToast();
  const handleSave = () => {
    const payload = {
      user_id: "USR_00000002",
      field_type: formType === "Users" ? "user_fields" : "lead_fields",
      fields: JSON.stringify(temproaryFormFields),
    };
    callApi(
      createReferenceFields,
      payload,
      () => toast("Saved Successfully", "Form Configuration Saved!"),
      (error) => toast("Failed to save", error, "error")
    );
  };

  useEffect(() => {
    setTemproaryFormFields(selectedFormFields);
  }, [selectedFormFields]);

  const toggleFieldSelected = (
    fieldId,
    sectionIndex,
    setFormFields,
    newSelectedValue
  ) => {
    console.log(
      "fieldId",
      fieldId,
      "sectionIndex",
      sectionIndex,
      "newSelectedValue",
      newSelectedValue
    );
    setFormFields((prev) => {
      const newForm = [...prev];
      const updatedSection = { ...newForm[sectionIndex] };
      const updatedFields = [...updatedSection.fields];

      const index = updatedFields.findIndex((f) => f.id === fieldId);
      console.log("index value", index);
      if (index === -1) return prev;

      const updatedField = { ...updatedFields[index] };
      updatedField.selected = newSelectedValue;
      updatedFields[index] = updatedField;

      updatedSection.fields = updatedFields;
      newForm[sectionIndex] = updatedSection;
      return newForm;
    });
  };
  console.log("temproaryFormFields", temproaryFormFields);

  return (
    <div className="form-configuration">
      {/* ADD NEW SECTION MODAL */}
      <ModalWrapper
        displayModal={addSection}
        closeModal={() => setAddSection(false)}
        zIndex={100}
      >
        <AddNewSection
          closeModal={() => setAddSection((prev) => !prev)}
          setSelectedFormFields={setSelectedFormFields}
          setTemproaryFormFields={setTemproaryFormFields}
        />
      </ModalWrapper>
      {/* ADD NEW FIELD MODAL */}
      <SlidingFormWrapper
        displayForm={showAddField}
        closeForm={() => setShowAddField((prev) => !prev)}
        zIndex={100}
      >
        <AddNewField
          closeModal={() => setShowAddField((prev) => !prev)}
          selectedFormFields={temproaryFormFields}
          setSelectedFormFields={setSelectedFormFields}
          setTemproaryFormFields={setTemproaryFormFields}
        />
      </SlidingFormWrapper>
      <div className="form-configuration__header">
        <h4>Form Configuration</h4>
        <button
          className="btn btn__primary form-configuration__header-btn"
          onClick={handleSave}
        >
          Save
        </button>
      </div>
      <div className="form-configuration__body">
        <div className="form-configuration__menu">
          <button
            className="form-configuration__menu-btn"
            onClick={() => setShowAddField((prev) => !prev)}
          >
            <ImportSvg
              icon="plus"
              className="form-configuration__menu-btn--icon"
            />
            New Field
          </button>
          <button
            className="form-configuration__menu-btn"
            onClick={() => setAddSection((prev) => !prev)}
          >
            <ImportSvg
              icon="plus"
              className="form-configuration__menu-btn--icon"
            />
            New Section
          </button>
          <button
            className="form-configuration__menu-btn"
            onClick={() => setShowPreview((prev) => !prev)}
          >
            <ImportSvg
              icon="preview"
              className="form-configuration__menu-btn--preview"
            />
            Preview
          </button>
          <div className="form-configuration__menu-dropdown">
            <CustomDropDown
              variant="small"
              options={["Users", "Leads"]}
              value={formType}
              onChange={(e) => setFormType(e.target.value)}
            />
          </div>
        </div>
        <div className="form-configuration__container">
          <div className="form-configuration__available-fields">
            <InfoHeader
              heading="Choose Fields"
              description="You can drag and drop fields"
              count={count.available}
            />
            {selectedFormFields?.map((section, sectionIndex) => (
              <div
                className="form-configuration__form-section"
                key={`sectionIndex-${sectionIndex}`}
              >
                <div className="form-configuration__form-section-header">
                  <h5>{section.section_name}</h5>
                  <ImportSvg
                    icon="delete-icon"
                    className="form-configuration__delete-icon"
                    onClick={() => {
                      if (section.mandatory) {
                        toast(
                          "Cannot delete default sections",
                          "Default sections cannot be deleted",
                          "error"
                        );
                        return;
                      }
                      setSelectedFormFields((prev) => {
                        return prev.filter(
                          (s) => s.section_name !== section.section_name
                        );
                      });
                      setTemproaryFormFields((prev) => {
                        return prev.filter(
                          (s) => s.section_name !== section.section_name
                        );
                      });
                    }}
                  />
                </div>
                <div className="form-configuration__form-section-fields">
                  {section?.fields?.map((field, fieldIndex) => (
                    <div
                      className="form-configuration__form-section-field"
                      key={`fieldIndex-${sectionIndex}-${fieldIndex}`}
                      onClick={() => {
                        if (field.required) return;

                        const newSelectedValue = !field.selected;

                        toggleFieldSelected(
                          field.id,
                          sectionIndex,
                          setSelectedFormFields,
                          newSelectedValue
                        );
                        toggleFieldSelected(
                          field.id,
                          sectionIndex,
                          setTemproaryFormFields,
                          newSelectedValue
                        );
                      }}
                    >
                      <ToggleButton
                        size="small"
                        disabled={field.required}
                        value={field.required ? true : !!field.selected}
                        onChange={() => {
                          if (field.required) return;

                          const newSelectedValue = !field.selected;

                          toggleFieldSelected(
                            field.id,
                            sectionIndex,
                            setSelectedFormFields,
                            newSelectedValue
                          );
                          toggleFieldSelected(
                            field.id,
                            sectionIndex,
                            setTemproaryFormFields,
                            newSelectedValue
                          );
                        }}
                      />
                      <div>{field.label}</div>
                      <div className="form-configuration__form-section-field-icons-container">
                        <ImportSvg
                          icon="delete-icon"
                          className="form-configuration__form-section-field-icon"
                          onClick={(e) => {
                            if (field.default) {
                              e.stopPropagation();
                              toast(
                                "Cannot delete default fields",
                                "Default fields cannot be deleted",
                                "error"
                              );
                              return;
                            }

                            setSelectedFormFields((prev) => {
                              const newForm = [...prev];
                              const updatedSection = {
                                ...newForm[sectionIndex],
                              };
                              const updatedFields = [...updatedSection.fields];

                              const index = updatedFields.findIndex(
                                (f) => f.id === field.id
                              );
                              if (index === -1) return prev;

                              updatedFields.splice(index, 1);

                              updatedSection.fields = updatedFields;
                              newForm[sectionIndex] = updatedSection;
                              return newForm;
                            });
                            setTemproaryFormFields((prev) => {
                              const newForm = [...prev];
                              const updatedSection = {
                                ...newForm[sectionIndex],
                              };
                              const updatedFields = [...updatedSection.fields];

                              const index = updatedFields.findIndex(
                                (f) => f.id === field.id
                              );
                              if (index === -1) return prev;

                              updatedFields.splice(index, 1);

                              updatedSection.fields = updatedFields;
                              newForm[sectionIndex] = updatedSection;
                              return newForm;
                            });
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          <div className="form-configuration__selected-fields">
            <InfoHeader
              heading="Selected Fields"
              description="Rearrange fields by dragging and dropping"
              count={count.selected}
            />
            {temproaryFormFields?.map((section, sectionIndex) => (
              <div
                key={`section-wrapper-${sectionIndex}`}
                className="form-configuration__form-section"
              >
                <h5>{section.section_name}</h5>
                <Reorder.Group
                  axis="y"
                  values={(section.fields || [])
                    ?.filter((field) => field.selected)
                    ?.map((f) => f.id)}
                  onReorder={(newFieldIds) =>
                    handleReorder(sectionIndex, newFieldIds)
                  }
                  className="form-configuration__form-section-fields"
                >
                  {section.fields
                    ?.filter((field) => field.selected)
                    ?.map((field) => (
                      <Reorder.Item value={field.id} key={field.id} layout>
                        <div className="form-configuration__form-section-field">
                          <div>{field.label}</div>
                        </div>
                      </Reorder.Item>
                    ))}
                </Reorder.Group>
              </div>
            ))}
          </div>
          <div
            className={`form-configuration__form-preview ${
              showPreview
                ? "form-configuration__form-preview--visible"
                : "form-configuration__form-preview--hidden"
            }`}
          >
            <div className="form-configuration__form-preview-container">
              <InfoHeader
                heading="Preview"
                description="You can drag and drop fields"
                count={count.selected}
              />
              <div className="form-configuration__form-preview-fields">
                {temproaryFormFields?.map((section, sectionIndex) => (
                  <div
                    className="form-configuration__form-preview-section"
                    key={`preview-sectionIndex-${sectionIndex}`}
                  >
                    <div className="form-configuration__form-preview-section-header">
                      <h5>{section.section_name}</h5>
                      <div className="form-configuration__form-preview-section-header--view">
                        <ColumnView
                          currentView={section.column_type}
                          handleToggle={(view) => {
                            setTemproaryFormFields((prev) => {
                              const newForm = [...prev];
                              const sectionCopy = { ...newForm[sectionIndex] };
                              sectionCopy.column_type = view;
                              newForm[sectionIndex] = sectionCopy;
                              return newForm;
                            });
                          }}
                        />
                      </div>
                    </div>
                    <div className="form-configuration__form-preview-section-fields">
                      {(() => {
                        const filteredFields =
                          section.fields?.filter(
                            (field) => field.selected || field.required
                          ) || [];

                        const groupedFields = [];
                        for (let i = 0; i < filteredFields.length; i += 2) {
                          groupedFields.push(filteredFields.slice(i, i + 2));
                        }

                        return groupedFields.map((group, groupIndex) => (
                          <div
                            className={`form-configuration__form-preview-section-field-group ${
                              section.column_type?.toLowerCase() === "double"
                                ? "form-configuration__form-preview-section-field-group--double"
                                : ""
                            }`}
                            key={`field-group-${sectionIndex}-${groupIndex}`}
                          >
                            {group.map((field, fieldIndex) => {
                              const textFieldTypes = [
                                "text",
                                "email",
                                "password",
                                "phone",
                              ];
                              const selectTypes = ["select", "multi-select"];
                              if (
                                textFieldTypes.includes(
                                  field.type?.toLowerCase()
                                )
                              ) {
                                return (
                                  <TextInput
                                    key={`preview-field-${sectionIndex}-${groupIndex}-${fieldIndex}`}
                                    label={field.label}
                                    placeholder={field.placeholder}
                                    value=""
                                    onChange={() => {}}
                                  />
                                );
                              }
                              if (selectTypes.includes(field.type)) {
                                return (
                                  <CustomDropDown
                                    variant="small"
                                    key={`preview-field-${sectionIndex}-${groupIndex}-${fieldIndex}`}
                                    label={field.label}
                                    options={field.options.map(
                                      (option) =>
                                        option.label || option.role_name
                                    )}
                                    value=""
                                    onChange={() => {}}
                                    disabled
                                  />
                                );
                              }
                            })}
                          </div>
                        ));
                      })()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormConfiguration;
