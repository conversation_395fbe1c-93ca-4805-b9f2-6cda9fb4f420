.form-configuration {
  display: flex;
  flex-direction: column;
  flex: 1;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 0.5rem 0;
    color: var(--color-text);
    &::after {
      position: absolute;
      content: "";
      display: block;
      width: 100%;
      height: 1px;
      background: var(--color-secondary);
      top: 100%;
      left: 0;
    }
  }

  &__header-btn {
    border-radius: 0.5rem;
  }

  &__body {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
    padding: 0.5rem 0;
  }

  &__menu {
    display: flex;
    flex-direction: row-reverse;
    gap: 0.5rem;
    padding: 0.5rem 0;
  }
  &__menu-dropdown {
    margin-right: auto;
    margin-left: 0.5rem;
  }

  &__menu-btn {
    border-radius: 0.5rem;
    gap: 0.5rem;
    display: flex;
    align-items: center;
    border: 1px solid var(--color-secondary);
    padding: 0.25rem 1rem;
    color: var(--color-text);
    &--icon {
      width: 1.2rem;
      height: 1.2rem;
    }

    &--preview {
      width: 2rem;
      height: 2rem;
    }
  }

  &__container {
    display: flex;
    gap: 1rem;
    flex: 1;
    color: var(--color-text);
  }

  &__available-fields {
    min-width: 28rem;
    border: 1px solid var(--color-secondary);
    border-radius: 0.5rem;
  }

  &__selected-fields {
    min-width: 28rem;
    border: 1px solid var(--color-secondary);
    border-radius: 0.5rem;
  }

  &__form-preview {
    border-radius: 0.5rem;
    display: grid;
    grid-template-columns: 0fr;
    transition: all 0.3s ease-in-out;
    overflow: hidden;

    &--hidden {
      grid-template-columns: 0fr;
      min-width: 0;
    }

    &--visible {
      grid-template-columns: 1fr;
      min-width: 40rem;
      border: 1px solid var(--color-secondary);
    }
  }

  &__form-preview-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow: hidden;
  }

  &__form-preview-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
  }

  &__form-preview-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__form-preview-section-fields {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
  }

  &__form-preview-section-field-group {
    display: flex;
    flex-direction: column;
    gap: 1.75rem;
    margin-top: 1rem;
    &--double {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 1.5rem;
      > * {
        flex: 0 0 calc(50% - 0.75rem);
        max-width: calc(50% - 0.75rem);
      }
    }
  }
  //Info header
  &__info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
  }

  &__info-header-container {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
  }

  &__info-header-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-text);
  }

  &__info-header-subtitle {
    font-size: 1rem;
    font-weight: 400;
    color: var(--color-text-secondary);
  }

  &__info-header-count {
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    background: var(--color-secondary);
  }

  //add section
  &__add-new-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
    padding: 0.5rem 1rem;
    min-width: 35rem;
    color: var(--color-text);
    .text-input {
      width: 100%;
    }

    .custom-dropdown {
      width: 100%;
    }
  }

  &__add-new-heading {
    font-size: 2rem;
    font-weight: 600;
  }
  &__add-new-buttons {
    display: flex;
    flex-direction: row-reverse;
    gap: 1rem;
    button {
      border-radius: 0.4rem;
    }
  }
  &__form-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
  }
  &__form-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &:hover {
      .form-configuration__delete-icon {
        opacity: 1;
      }
    }
  }

  &__delete-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  &__form-section-fields {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  &__form-section-field {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--color-stroke);
    border-radius: 0.5rem;
    &:hover {
      background-color: var(--color-secondary);
      cursor: pointer;
      .form-configuration__form-section-field-icon {
        opacity: 1;
        pointer-events: unset;
      }
    }
  }

  &__form-section-field-icons-container {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
  }

  &__form-section-field-icon {
    width: 1.4rem;
    height: 1.4rem;
    color: var(--color-primary);
    opacity: 0;
    pointer-events: none;
  }
}
