import { useEffect } from "react";
import { referenceFields } from "../../store/actions/fromConfigureAction";
import useApiHandler from "../../utilities/hooks/useApiHandler";
import { useSelector } from "react-redux";
import useOnceOnMount from "../../utilities/hooks/useOnceOnMount";
import { useToast } from "../../utilities/hooks/useToast";

const useFetchReferenceFields = () => {
  const callApi = useApiHandler();
  const formFields = useSelector(
    (state) => state.formConfigure.referenceFields
  );
  const toast = useToast();
  useOnceOnMount(() => {
    if (!formFields?.data?.reference_fields?.length) {
      callApi(
        referenceFields,
        { company_id: "COM_00000001" },
        () => null,
        (err) => toast("Failed to fetch reference fields.", err, "error")
      );
    }
  }, [callApi, formFields]);
};

export default useFetchReferenceFields;
