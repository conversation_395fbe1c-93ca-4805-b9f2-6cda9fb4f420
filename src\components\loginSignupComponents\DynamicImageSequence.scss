.sequence {
  display: flex;
  width: 55%;
  max-width: 35rem;
  min-height: 40rem;
  position: relative;
  [data-theme="dark"] &__slide {
    background-color: var(--color-secondary-darker);
  }
  &__slide {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    transform: translateX(100%);
    transition: transform 0.3s 0.2s ease-in-out, opacity 0.3s;
    background-color: var(--color-primary-darker);
    border-radius: 2.5rem;
    &--active {
      opacity: 1;
      transform: translateX(0);
      box-shadow: var(--shadow);
    }
    &--inactive {
      opacity: 0;
      transform: translateX(-100%);
    }
  }
  &__container {
    flex-direction: column;
    gap: 2rem;
    overflow: hidden;
  }
  &__img {
    position: absolute;
    right: 0;
    bottom: 0;
    overflow: hidden;
    border-radius: 2.5rem;
    width: 100%;
    height: 50rem;
    img {
      position: absolute;
      right: 0;
      bottom: 0;
    }
    .img1 {
      width: 25rem;
    }
    .img2 {
      width: 40rem;
    }
    .img3 {
      width: 55rem;
    }
  }
  &__image {
    opacity: 0;
    transform: translate(0, 2rem);
    transition: opacity 1s ease, transform 1s ease;
    &--active {
      transform: translate(0, 0);
      opacity: 1;
    }
  }
  &__msg {
    position: absolute;
    background-color: var(--color-extreme-2);
    top: 50%;
    left: 10%;
    opacity: 0;
    transform: translate(0%, -50%);
    padding: 1.5rem;
    padding-right: 8rem;
    border-radius: 1rem;
    transition: opacity 1s ease 1s, transform 1s ease 1s;
    &--active {
      opacity: 1;
      transform: translate(-50%, -50%);
    }
  }
  &__quote {
    position: absolute;
    bottom: 10%;
    right: 0%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 10rem;
    height: 10rem;
    background-color: var(--color-extreme-2);
    transform: translate(0%, 0%);
    scale: 0;
    border-radius: 50%;
    transition: scale 1s ease 1s, transform 1s ease 1s;
    &--active {
      transform: translate(50%, -50%);
      scale: 1;
    }
  }
  &__text {
    font-weight: 600;
    text-align: center;
  }
  &__nav {
    display: flex;
    height: 1rem;
    gap: 1rem;
  }
  [data-theme="dark"] &__nav-item {
    background-color: var(--color-stroke-hover);
    &--active {
      background-color: var(--color-primary-darker);
    }
  }
  &__nav-item {
    background-color: var(--color-extreme-2-hover);
    width: 2rem;
    border-radius: 1rem;
    transition: color 0.2s, width 0.3s;
    &--active {
      width: 8rem;
      background-color: var(--color-primary-darker);
      box-shadow: var(--shadow);
    }
  }
}

@keyframes moveLeftRightCenter {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-150px);
  }
  50% {
    transform: translateX(150px);
  }
  100% {
    transform: translateX(0);
  }
}
