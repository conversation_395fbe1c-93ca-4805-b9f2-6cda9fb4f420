import React, { useState, useEffect } from "react";
import TextInput from "../shared/Input Components/textinput/TextInput";
import { useNavigate } from "react-router-dom";
import useApiHandler from "../../utilities/hooks/useApiHandler";
import { loginView } from "../../store/actions/loginSignupAction";
import ImportSvg from "../shared/importsvg/ImportSvg";
import { payload } from "../../utilities/payload";
import ButtonLoader from "../shared/Loader/ButtonLoader/ButtonLoader";
import { useSelector } from "react-redux";
import { useToast } from "../../utilities/hooks/useToast";
import MainLogo from "../shared/Components/mainlogo/MainLogo";

const Login = () => {
  const loading = useSelector((state) => state.loginSignup.loginView.isLoading);
  const [formData, setFormData] = useState(payload.loginView);
  const toast = useToast();
  const callApi = useApiHandler();
  const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  const [rememberMe, setRememberMe] = useState(
    localStorage.getItem("crm_remember_me")
  );

  const [errors, setErrors] = useState({ user_email: "", password: "" });
  const [masked, setMasked] = useState(true);
  const navigate = useNavigate();

  // Event Handlers
  const handleChange = ({ target: { id, value } }) => {
    setFormData((prev) => ({ ...prev, [id]: value }));
    setErrors((prevErrors) => ({ ...prevErrors, [id]: "" }));
  };
  const handleCheckboxChange = () => {
    setRememberMe((prev) => !prev);
  };

  const handleLogin = (event) => {
    event.preventDefault();

    if (!validateInputs()) return;

    const success = (res) => {
      console.log(res);
      if (rememberMe) {
        localStorage.setItem("crm_user_email", formData.user_email);
        localStorage.setItem("crm_password", formData.password);
        localStorage.setItem("crm_remember_me", rememberMe);
      } else {
        ["crm_user_email", "crm_password", "crm_remember_me"].forEach((key) =>
          localStorage.removeItem(key)
        );
      }
      if (res?.user?.is_first_login) {
        navigate("/account/user-set-password", { state: res.user.user_email });
      } else {
        const { user_email, user_id, first_name } = res.user;
        localStorage.setItem("token", res.token);
        localStorage.setItem("user_email", user_email);
        localStorage.setItem("user_id", user_id);
        localStorage.setItem("first_name", first_name);
        window.location.href = "/";
      }
    };
    callApi(
      loginView,
      formData,
      (response) => success(response?.data),
      (error) => {
        toast("Login Failed", error, "error");
      }
    );
  };

  const handleNavigate = (route) => {
    navigate(route);
  };

  const validateInputs = () => {
    const newErrors = {};
    if (!formData.user_email)
      newErrors.user_email = "Email field cannot be empty.";
    else if (!EMAIL_REGEX.test(formData.user_email))
      newErrors.user_email = "Please enter a valid email address.";

    if (!formData.password)
      newErrors.password = "Password field cannot be empty.";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  useEffect(() => {
    if (localStorage.getItem("crm_remember_me") === "true") {
      // setFormData(localStorage.getItem("crm_login_data"));

      setFormData((prevFormData) => ({
        ...prevFormData, // Keep existing fields
        user_email: localStorage.getItem("crm_user_email"),
        password: localStorage.getItem("crm_password"),
        // Update user_email field
      }));
    }
  }, []);

  const renderAdornment = () =>
    masked ? (
      <ImportSvg
        icon="locked"
        className="unauthorized__form-input--adornment"
        onClick={() => {
          setMasked(false);
        }}
      />
    ) : (
      <ImportSvg
        icon="unlocked"
        className="unauthorized__form-input--adornment "
        onClick={() => {
          setMasked(true);
        }}
      />
    );

  return (
    <div className="unauthorized">
      <MainLogo />
      <div className="unauthorized__heading-container">
        <p className="unauthorized__heading">Log In to your accounts</p>
        <p className="unauthorized__sub-heading">
          Enter your credentials to jump in and begin your adventure.
        </p>
      </div>

      <form className="unauthorized__form-container" onSubmit={handleLogin}>
        <div className="unauthorized__form-input">
          <TextInput
            type="text"
            label="Registered Mail Id"
            id="user_email"
            name="user_email"
            value={formData.user_email}
            placeholder="Enter your registered mail id"
            onChange={handleChange}
            infoType="error"
            infoMsg={errors.user_email}
            variant="large"
          />
        </div>
        <div className="unauthorized__form-input">
          <TextInput
            type={masked ? "password" : "text"}
            label="Password"
            name="password"
            id="password"
            value={formData.password}
            placeholder="Enter your password"
            onChange={handleChange}
            infoType="error"
            infoMsg={errors.password}
            variant="large"
          />

          {renderAdornment()}
        </div>
        <div className="login__user-assist">
          <div className="login__checkbox-container">
            <input
              type="checkbox"
              id="remember-password"
              name="rememberMe"
              checked={rememberMe}
              onChange={handleCheckboxChange}
            ></input>
            <label
              htmlFor="remember-password"
              className="unauthorized__remember-password-label"
            >
              Remember Password
            </label>
          </div>
          <div
            className="login__forgot-password-btn"
            onClick={() => handleNavigate("forgot-password")}
          >
            Forgot Password ?
          </div>
        </div>
        <button
          className="btn btn__primary btn__elevated btn__progress unauthorized__form-btn login__login-btn"
          disabled={loading}
          onClick={handleLogin}
        >
          {!loading ? (
            "Log in"
          ) : (
            <ButtonLoader
              height="2rem"
              width="2rem"
              loaderColor="white"
              loading={loading}
            />
          )}
        </button>
        <div className="unauthorized__quicklink-container">
          <div className="unauthorized__quicklink">
            New here &nbsp;
            <button
              className="unauthorized__quicklink-btn"
              type="button"
              onClick={() => handleNavigate("account/signup")}
            >
              Sign up
            </button>
            &nbsp; in a snap!
          </div>
        </div>
      </form>
    </div>
  );
};

export default Login;
