import React, { useEffect, useState } from "react";
import TextInput from "../shared/Input Components/textinput/TextInput";
import useApiHander from "../../utilities/hooks/useApiHandler";
import { payload } from "../../utilities/payload";
import ImportSvg from "../shared/importsvg/ImportSvg";
import PasswordRequirement from "./PasswordRequirement";
import { useNavigate } from "react-router-dom";
import ButtonLoader from "../shared/Loader/ButtonLoader/ButtonLoader";
import { useSelector } from "react-redux";
import { updatePassword } from "../../store/actions/loginSignupAction";
import { useToast } from "../../utilities/hooks/useToast";
import MainLogo from "../shared/Components/mainlogo/MainLogo";

function ResetPasswordForm({ userMail, compContent, verificationResponse }) {
  const toast = useToast();
  const [showPassword, setShowPassword] = useState({
    new: false,
    confirm: false,
  });

  const [formData, setFormData] = useState({
    ...payload.updatePassword,
    user_email: userMail,
  });
  const [newPassword, setNewPassword] = useState("");
  const [error, setError] = useState({
    upperCase: false,
    lowerCase: false,
    specialCharacter: false,
    number: false,
    eightCharacters: false,
  });
  const callApi = useApiHander();
  const navigate = useNavigate();

  useEffect(() => {
    setFormData((prev) => ({ ...prev, user_email: userMail }));
  }, [userMail]);

  useEffect(() => {
    if (compContent?.componentName === "user reset") {
      setFormData((prev) => ({ ...prev, first_login: true }));
    }
  }, [compContent]);

  const validatePassword = (password) => {
    const validations = {
      upperCase: /[A-Z]/.test(password),
      lowerCase: /[a-z]/.test(password),
      specialCharacter: /[@$!%*?&#]/.test(password),
      number: /\d/.test(password),
      eightCharacters: password.length >= 8,
    };

    setError(validations);
    return validations;
  };

  const updatePasswordLoading = useSelector(
    (state) => state.loginSignup.updatePassword.isLoading
  );

  const alertValidationErrors = (validations) => {
    if (!validations.eightCharacters) {
      // alert("Password must be at least 8 characters long.");

      toast(
        "Invalid Password",
        "Password must be at least 8 characters long.",
        "error"
      );
      return false;
    }
    if (!validations.upperCase) {
      // alert("Password must include at least one uppercase letter.");

      toast(
        "Invalid Password",
        "Password must include at least one uppercase letter.",
        "error"
      );
      return false;
    }
    if (!validations.lowerCase) {
      // alert("Password must include at least one lowercase letter.");

      toast(
        "Invalid Password",
        "Password must include at least one lowercase letter.",
        "error"
      );
      return false;
    }
    if (!validations.number) {
      // alert("Password must include at least one number.");

      toast(
        "Invalid Password",
        "Password must include at least one number.",
        "error"
      );
      return false;
    }
    if (!validations.specialCharacter) {
      // alert("Password must include at least one special character.");

      toast(
        "Invalid Password",
        "Password must include at least one special character.",
        "error"
      );
      return false;
    }
    return true;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (newPassword !== formData.password) {
      toast("Password Mismatch", "Passwords do not match!", "error");
      return;
    }

    const validations = validatePassword(newPassword);
    if (alertValidationErrors(validations)) {
      if (
        compContent?.componentName === "reset" ||
        compContent?.componentName === "user reset"
      ) {
        callApi(
          updatePassword,
          formData,
          (res) => {
            toast("Password Updated", "Password Reset successful!");
            // navigate("/");
            window.location.href = "/";
          },
          (err) => {
            toast("Password Error", err, "error");
          }
        );
      } else if (compContent?.componentName === "set") {
        navigate("/account", {
          state: { userMail, password: formData?.password },
        });
      } else if (compContent?.componentName === "company reset") {
        navigate("/register-company", {
          state: {
            password: formData?.password,
            verificationResponse: verificationResponse,
            userMail,
          },
        });
      }
    }
  };

  const handleChange = (e) => {
    const { id, value } = e.target;

    if (id === "new_password") {
      setNewPassword(value);
      validatePassword(value);
    } else {
      setFormData((prev) => ({ ...prev, [id]: value }));
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPassword((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  return (
    <div className="unauthorized">
      <MainLogo />
      <div className="unauthorized__heading-container">
        <h1 className="unauthorized__heading">{compContent?.heading}</h1>
        <p className="unauthorized__sub-heading">{compContent?.subHeading}</p>
      </div>

      <form onSubmit={handleSubmit} className="unauthorized__form-container">
        <div className="unauthorized__form-input">
          <TextInput
            value={userMail}
            type="text"
            disabled
            label="Registered Mail ID"
            placeholder="Registered Mail ID"
            variant="large"
          />
        </div>

        <div className="unauthorized__form-input">
          <TextInput
            value={newPassword}
            id="new_password"
            label="New Password"
            placeholder="New Password"
            onChange={handleChange}
            type={showPassword.new ? "text" : "password"}
            variant="large"
          />
          <ImportSvg
            className="unauthorized__form-input--adornment"
            icon={showPassword.new ? "unlocked" : "locked"}
            onClick={() => togglePasswordVisibility("new")}
          />
        </div>

        <div className="unauthorized__form-input">
          <TextInput
            value={formData.password}
            id="password"
            label="Confirm Password"
            placeholder="Confirm Password"
            onChange={handleChange}
            type={showPassword.confirm ? "text" : "password"}
            variant="large"
          />
          <ImportSvg
            className="unauthorized__form-input--adornment"
            icon={showPassword.confirm ? "unlocked" : "locked"}
            onClick={() => togglePasswordVisibility("confirm")}
          />
        </div>

        <button
          type="submit"
          className="btn btn__primary reset__btn"
          disabled={updatePasswordLoading}
        >
          {updatePasswordLoading ? (
            <ButtonLoader
              loaderColor="white"
              loading={updatePasswordLoading}
              width="2.5rem"
              height="2.5rem"
            />
          ) : (
            compContent?.buttonName
          )}
        </button>
        <PasswordRequirement error={error} />
      </form>
    </div>
  );
}

export default ResetPasswordForm;
