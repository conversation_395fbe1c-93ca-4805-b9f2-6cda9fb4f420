import React, { useEffect, useState } from "react";
import CompanyProfileImageEditor from "./CompanyProfileImageEditor/CompanyProfileImageEditor";
import "./CompanyInformationForm.scss";
import TextInput from "../shared/Input Components/textinput/TextInput";
import TextArea from "../shared/Input Components/TextArea/TextArea";
import CustomDropDown from "../shared/Input Components/DropDown/CustomDropDown";
import { useLocation, useNavigate } from "react-router-dom";
import { payload } from "../../utilities/payload";
import useFormData from "../../utilities/hooks/useFormData";
import regexInputTypes from "../../utilities/constants/regexInputTypes";
import inputRestrictingRegex from "../../utilities/constants/inputRestrictingRegex";
import useCustomOnChange from "./useCustomOnChange";
import { industryTypes } from "../../utilities/constants/options";

const CompanyInformationForm = () => {
  const location = useLocation();
  const { password, verificationResponse, userMail } = location.state || {};

  const navigate = useNavigate();
  const localformData = JSON.parse(
    localStorage.getItem("data_company_register")
  );
  const { formData, handleChange, setFormData } = useFormData(
    localformData ? localformData : payload.companyRegister
  );
  const { customOnChange, setActiveComponentFn } = useCustomOnChange(
    formData,
    handleChange
  );

  useEffect(() => {
    const orgProvidedData = verificationResponse?.[0];
    setFormData((prev) => ({
      ...prev,
      password: password,
      org_id: orgProvidedData?.org_id,
      company_email: formData?.company_email || userMail,
      modules: orgProvidedData?.modules,
    }));
    console.log(orgProvidedData);
  }, [password, verificationResponse]);
  useEffect(() => {
    const verifyBlobUrl = async () => {
      if (formData.profile?.startsWith("blob:")) {
        try {
          const response = await fetch(formData.profile);
          if (!response.ok) {
            throw new Error("Failed to fetch blob");
          }
        } catch (error) {
          setFormData((prev) => ({ ...prev, profile: null }));
        }
      }
    };

    verifyBlobUrl();
  }, [formData.profile]);

  const [formErrors, setFormError] = useState();
  console.log(formData);
  const validateForm = () => {
    const errors = {};

    if (!formData.company_name) {
      errors.company_name = "Company name is required";
    } else if (!regexInputTypes.companyName.test(formData.company_name)) {
      errors.company_name = "Invalid Company Name";
    }

    if (!formData.company_email) {
      errors.company_email = "Email is required";
    } else if (!regexInputTypes.email.test(formData.company_email)) {
      errors.company_email = "Invalid Email Id";
    }

    if (!formData.phone) {
      errors.phone = "Phone number is required";
    } else if (!regexInputTypes.phone.test(formData.phone)) {
      errors.phone = "Invalid Phone Number";
    }

    setFormError(errors);

    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    localStorage.setItem("data_company_register", JSON.stringify(formData));
    const isValid = validateForm();
    if (isValid) {
      navigate("/register-company/address", {
        state: { prevFormData: { ...formData } },
      });
    }
  };

  return (
    <form className="company-reg-form" onSubmit={handleSubmit}>
      <div className="company-reg-form__heading-container">
        <p className="company-reg-form__page-count">1 of 4</p>
        <p className="company-reg-form__heading">Company Information</p>
        <p className="company-reg-form__sub-heading">
          Let's get your company dashboard up and running. Follow these quick
          steps!
        </p>
      </div>
      <CompanyProfileImageEditor
        companyName={formData.company_name}
        onChange={(value) => {
          setFormData((prev) => ({
            ...prev,
            profile: value,
          }));
          setActiveComponentFn("profile");
        }}
        removeProfile={() =>
          setFormData((prev) => ({
            ...prev,
            profile: null,
          }))
        }
      />
      <div className="company-reg-form__form-fields">
        <TextInput
          className="company-reg-form__input-field"
          onChange={customOnChange}
          placeholder="Enter Company Name"
          label="Company Name"
          id="company_name"
          value={formData.company_name}
          infoType={formErrors?.company_name && "error"}
          infoMsg={formErrors?.company_name}
          regex={inputRestrictingRegex.characterField}
        />
        <CustomDropDown
          options={industryTypes}
          label="Type of Company"
          className="company-reg-form__dropdown-field"
          id="industry_type"
          value={formData.industry_type}
          onChange={customOnChange}
        />
        <TextInput
          className="company-reg-form__input-field"
          onChange={customOnChange}
          placeholder="Enter Email Id"
          label="Company Email Id"
          id="company_email"
          value={formData.company_email}
          infoType={formErrors?.company_email && "error"}
          infoMsg={formErrors?.company_email}
          regex={inputRestrictingRegex.emailField}
        />
        <TextInput
          type="tel"
          className="company-reg-form__input-field"
          onChange={customOnChange}
          placeholder="Enter Phone Number"
          label="Company Phone Number"
          id="phone"
          value={formData.phone}
          infoType={formErrors?.phone && "error"}
          infoMsg={formErrors?.phone}
          regex={inputRestrictingRegex.integerField}
        />
        <TextArea
          onChange={customOnChange}
          placeholder="Enter name here"
          label="Company Description"
          id="other_details.description"
          value={formData.other_details?.description}
        />
      </div>
      <button
        type="submit"
        className="btn btn__primary btn__elevated company-reg-form__btn"
      >
        Next
      </button>
    </form>
  );
};

export default CompanyInformationForm;
