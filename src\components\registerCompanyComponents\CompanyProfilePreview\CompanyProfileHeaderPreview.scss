.company-profile-card-preview {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  border-radius: 0.5rem;
  gap: 1.5rem;
  background-color: var(--color-extreme-2);
  position: relative;
  overflow: hidden;
  align-items: center;
  &__logo {
    display: flex;
    width: 12rem;
    height: 12rem;
    border-radius: 1rem;
    // border: 1px solid var(--color-secondary);
    position: relative;
    overflow: hidden;
    font-size: 5rem;
    &--skeleton {
      width: 10rem !important;
      height: 10rem;
      border-radius: 0.5rem;
    }
  }

  &__details {
    flex: 1 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    padding-left: 0.5rem;
    max-width: 100%;
    &--skeleton {
      width: 100%;
    }
  }
  &__company-name {
    font-size: 1.3rem;
    max-width: 20rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    &--skeleton {
      max-width: 20rem;
      height: 2rem;
    }
  }

  &__description {
    color: var(--color-text-secondary);
    font-size: 1.1rem;
    overflow: hidden;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    flex: 1 1;
    max-width: 30rem;
    padding: 0 0.2rem;
    text-overflow: ellipsis;
    &--skeleton {
      width: 60%;
      max-width: 40rem;
      word-wrap: wrap;
      height: 2rem;
    }
  }

  &__company-type {
    font-size: 1.3rem;
    color: var(--color-text);
  }
  &__category {
    display: inline-block;
    font-size: 0.875rem;
    background: var(--color-yellow-secondary);
    padding: 0.3rem 0.5rem;
    border-radius: 40rem;
    border: 1px solid var(--color-yellow-primary);
    width: max-content;
    &--skeleton {
      width: 10rem !important ;
    }
  }
  //Social Links Elements
  &__social-links-container {
    display: flex;
    width: 100%;
    padding: 0 0.3rem;
    border: 1px solid var(--color-secondary);
    border-radius: 0.5rem;
    justify-content: space-between;
    gap: 0.5rem;
    max-width: 50rem;
  }
  &__link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: var(--color-text-secondary);
    overflow: hidden;
    width: fit-content;
    flex: 1;
  }
  &__social-icon {
    width: 1.2rem;
    height: 1.2rem;
    color: var(--color-primary);
    &--skeleton {
      width: 1.2rem !important;
      height: 1.2rem;
    }
  }
  &__social-icon-content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 1.1rem;
    color: var(--color-text);
    width: 15rem;
    &--skeleton {
      min-width: 6rem;
      height: inherit;
    }
  }

  //Contact Links Elements
  &__contact {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-left: 1rem;
    border-left: 1px solid var(--color-secondary);
    overflow: hidden;
    justify-content: center;
    width: 18rem;
  }
}
