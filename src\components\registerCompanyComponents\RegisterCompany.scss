.register-company {
  background-color: var(--color-background);
  flex: 1;
  padding: 2rem 6rem 0 4rem;
  display: flex;
  flex-direction: column;
  &__header {
    display: flex;
    align-items: center;
  }
  &__header-topic {
    position: relative;
    h2 {
      font-size: 3.6rem;
    }
  }
  &__form-preview {
    flex: 1;
    display: flex;
    overflow: auto;
    @media (max-width: 768px) {
      align-items: center;
    }
  }
  &__form {
    width: 40%;
    max-width: 56rem;
    overflow: auto;
    @media (max-width: 768px) {
      width: 100%;
      max-width: none;
    }
  }
  &__preview {
    flex: 1;
    margin: 4rem 1rem 0 4rem;
    display: flex;
    flex-direction: column;
    border: 1rem solid var(--color-text);
    border-bottom: none;
    border-radius: 3rem 3rem 0 0;
    overflow: hidden;
    overflow: auto;
    position: relative;
    pointer-events: none;
    &::-webkit-scrollbar {
      display: none;
    }
    @media (max-width: 768px) {
      display: none;
    }
  }
  &__toast-container {
    position: absolute;
    top: 0;
  }
}
