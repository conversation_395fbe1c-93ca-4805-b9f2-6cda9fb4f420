import React, { useState } from "react";
import "./CompanyCard.scss";
import ImportSvg from "../../importsvg/ImportSvg";
import { useNavigate } from "react-router-dom";
function AddCompanyCard({ disabled = false }) {
  const navigate = useNavigate();
  return (
    <div className={`company-card company-card--add-company ${!disabled && 'company-card__elevate'}`} onClick={() => navigate("/company/add-company")}>
      <div className="company-card__hovering-cont">
      <div className='company-card__info'>
        <div className='company-card__logo company-card__logo-dummy'>
          <ImportSvg
            icon='company'
            className='company-card__logo-svg  '
          />
        </div>
        <div className='company-card__info-grid'>
          <div className='company-card__info-grid-row'>
            <div className='company-card__info-icon'></div>
            <div className='company-card__info-details company-card__info-details-no-content'></div>
          </div>
          <div className='company-card__info-grid-row'>
            <div className='company-card__info-icon'></div>
            <div className='company-card__info-details company-card__info-details-no-content'></div>
          </div>
          <div className='company-card__info-grid-row'>
            <div className='company-card__info-icon'></div>
            <div className='company-card__info-details company-card__info-details-no-content'></div>
          </div>
        </div>
      </div>
      <div className='company-card__status'>
        <div className='company-card__info-details'>
          <p className='company-card__main-heading company-card__main-heading-dummy'>
            New Company
          </p>
        </div>
        <button
          disabled={disabled}
          className={`btn btn__primary company-card__button-with-svg`}
          onClick={() => navigate("/company/add-company")}>
          <ImportSvg
            icon='add-new'
            className='company-card__add-button'
          />
          Add New
        </button>
      </div>
      </div>
    </div>
  );
}

export default AddCompanyCard;
