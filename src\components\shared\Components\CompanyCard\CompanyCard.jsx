import React from "react";
import "./CompanyCard.scss";
import StatusBarComponent from "../StatusBar/StatusBarComponent";
import ImportSvg from "../../importsvg/ImportSvg";
import CompanyProfileImage from "../../../registerCompanyComponents/CompanyProfileImage/CompanyProfileImage";
import EmptyData from "../EmptyDataComponent/EmptyData";

function CompanyCard({ onClick, companyDetails }) {
  return (
    <div
      className='company-card scroll-into-view'
      onClick={onClick}>
      <div className='company-card__info'>
        <div className='company-card__logo'>
          <CompanyProfileImage
            imageUrl={companyDetails && companyDetails?.profile}
            companyName={companyDetails && companyDetails?.company_name}
          />
        </div>

        <div className='company-card__info-grid'>
          <div className='company-card__info-grid-row'>
            <ImportSvg
              icon='profile'
              className='company-card__svg'
            />
            <div className='company-card__info-details'>
              <p className='company-card__heading'>Admin Name</p>
              <p className='company-card__heading company-card__heading-content'>
                {companyDetails.admin_name || <EmptyData />}
              </p>
            </div>
          </div>
          <div className='company-card__info-grid-row'>
            <ImportSvg
              icon='calender'
              className='company-card__svg'
            />
            <div className='company-card__info-details'>
              <p className='company-card__heading'>Created On</p>
              <p className='company-card__heading company-card__heading-content'>
                {new Date(companyDetails.created_at).toLocaleDateString() || (
                  <EmptyData />
                )}
              </p>
            </div>
          </div>
          <div className='company-card__info-grid-row'>
            <ImportSvg
              icon='company'
              className='company-card__svg'
            />
            <div className='company-card__info-details'>
              <p className='company-card__heading'>Company Id</p>
              <p className='company-card__heading company-card__heading-content'>
                {companyDetails.company_id || <EmptyData />}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className='company-card__status'>
        <div className='company-card__company-name'>
          <h1 className='company-card__main-heading'>
            {companyDetails.company_name || (
              <EmptyData content={"name not found"} />
            )}
          </h1>
          <p className='company-card__sub-topic'>
            {companyDetails.company_email || <EmptyData />}
          </p>
        </div>
        <StatusBarComponent status={"active"} />
      </div>
    </div>
  );
}

export default CompanyCard;
