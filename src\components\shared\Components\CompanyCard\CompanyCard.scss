.company-card {
  display: flex;
  width: -webkit-fill-available;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  border-radius: 1.5rem;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  cursor: pointer;
  box-shadow: var(--shadow-primary-card);

  &__hovering-cont {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    width: 100%;
    height: 100%;
    &:hover {
      > .company-card__status {
        gap: 0;

        > .company-card__button-with-svg:not(:disabled) {
          background-color: var(--color-primary-hover);
          width: 100%;
        }

        > .company-card__button-with-svg:disabled
          ~ .company-card__info-details
          > .company-card__main-heading-dummy {
          display: none;
        }
      }
    }
  }

  &--add-company {
    border: 1px dashed var(--color-stroke);
  }
  &__elevate {
    transition: transform 0.3s;
    gap: 2.5rem;
    justify-content: space-between;
    &:hover {
      transform: translateY(-1rem);
      box-shadow: var(--shadow-primary);
    }
  }

  &__info {
    display: flex;
    width: 100%;
    gap: 2rem;
  }
  &__logo-svg {
    width: 5rem;
    height: 5rem;
    color: var(--color-extreme-2);
  }
  &__logo {
    width: 10rem;
    height: 10rem;
    border-radius: 1.5rem;
    object-fit: cover;
    object-position: center;
    font-size: 5rem;
  }
  &__logo-dummy {
    background-color: var(--color-stroke);
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__logo-duplicate {
    background-color: var(--color-secondary-darker);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 5rem;
    font-weight: 600;
    color: var(--color-primary);
  }
  &__info-grid {
    flex: 1 1;
    height: 10rem;
    display: grid;
    row-gap: 0.6rem;
    grid-template-rows: repeat(3, 1fr);
    overflow: hidden;
  }
  &__info-grid-row {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
    overflow: hidden;
  }
  &__svg {
    width: 2rem;
    height: 2rem;
    color: var(--color-primary);
  }
  &__info-icon {
    width: 3rem;
    height: 100%;
    background-color: var(--color-stroke);
    border-radius: 0.3rem;
  }
  &__info-details {
    flex: 1 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  &__info-details-no-content {
    height: 100%;
    background-color: var(--color-stroke);
    border-radius: 0.3rem;
  }
  &__status {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: space-between;
    overflow: hidden;
    &:has(:hover:not(:disabled)) {
      gap: 0rem;
    }
  }
  &__company-name {
    display: flex;
    flex: 1 1;
    width: 100%;
    // max-width: 23rem;
    flex-direction: column;
    overflow: hidden;
  }
  &__heading {
    color: var(--color-text-secondary);
    font-size: 1rem;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__heading-content {
    color: var(--color-text);
  }
  &__main-heading {
    font-size: 1.6rem;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--color-text);
  }
  &__main-heading-dummy {
    color: var(--color-stroke);
    font-weight: 700;
  }
  &__sub-topic {
    font-size: 1.2rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--color-text);
    font-weight: 500;
  }
  &__button-with-svg {
    width: 12rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    border-radius: 0.5rem;
    transition: width 0.3s ease;
    &:disabled {
      background-color: var(--color-stroke);
      color: var(--color-text);
      cursor: not-allowed;
    }
    &:disabled > .company-card__add-button {
      color: var(--color-text);
    }
    &:hover ~ .company-card__info-details {
      display: none;
    }
    &:hover:not(:disabled) {
      width: 100%;
    }
  }
  &__add-button {
    width: 1.5rem;
    height: 1.5rem;
  }
}
