import React from "react";
import "./CompanyCard.scss";
import './CompanyCardLoading.scss';
import Skeleton from "react-loading-skeleton";

function CompanyCardLoading() {
  return (
    <div className={`company-card company-card--add-company`}>
      <div className="company-card__info">
        <div className="company-card__logo company-card__logo">
          <Skeleton className="company-card__logo" />
        </div>
        <div className="company-card__info-grid">
          <div className="company-card__info-grid-row">
            <div className="company-card__skeleton-box">
              <Skeleton className=" company-card__skeleton" />
            </div>
            <div className="company-card__info-details-skeleton">
              <Skeleton className=" company-card__skeleton" />
            </div>
          </div>
          <div className="company-card__info-grid-row">
            <div className="company-card__skeleton-box">
              <Skeleton className=" company-card__skeleton" />
            </div>
            <div className="company-card__info-details-skeleton">
              <Skeleton className=" company-card__skeleton" />
            </div>
          </div>
          <div className="company-card__info-grid-row">
            <div className="company-card__skeleton-box">
              <Skeleton className=" company-card__skeleton" />
            </div>
            <div className="company-card__info-details-skeleton">
              <Skeleton className=" company-card__skeleton" />
            </div>
          </div>
        </div>
      </div>
      <div className="company-card__status-skeleton">
        <div className="company-card__company-name">
          <Skeleton className=" company-card__name-skeleton" />
          <Skeleton className=" company-card__mail-skeleton" />
        </div>
        <div className="company-card__status-bar-skeleton">
        <Skeleton className="company-card__skeleton" />
        </div>
      </div>
    </div>
  );
}

export default CompanyCardLoading;
