import React from "react";
import ImportSvg from "../../importsvg/ImportSvg";
import "./CustomToast.scss";
import { useDispatch, useSelector } from "react-redux";
import { removeToast } from "../../../../store/slice/generalSlice";

const CustomToast = () => {
  const toasts = useSelector((state) => state.general.toast);
  const dispatch = useDispatch();
  return (
    <article className='toast'>
      {toasts.map((toast) => (
        <div
          className='toast__container'
          key={toast.id}>
          <div
            className={`toast__icon-container toast__icon-container--${toast.type}`}>
            <ImportSvg
              icon={toast.type}
              className='toast__icon'
            />
          </div>
          <div className='toast__content'>
            <h4 className='toast__title'>{toast.title}</h4>
            <p className='toast__message'>{toast.message}</p>
          </div>
          <ImportSvg
            icon='close'
            className='toast__icon toast__close-btn'
            onClick={() => {
              dispatch(removeToast(toast.id));
            }}
          />
        </div>
      ))}
    </article>
  );
};

export default CustomToast;
