.toast {
  position: absolute;
  top: -1.5rem;
  right: calc(100% + 0.5rem);
  display: flex;
  flex-direction: column-reverse;
  gap: 1rem;
  padding: 1rem 0.5rem;
  overflow: hidden;
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 1) 12vh,
    rgba(0, 0, 0, 0) 22vh
  );
  mask-composite: intersect;
  z-index: 10000;
  &__container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background-color: var(--color-extreme-2);
    padding: 0.5rem;
    border-radius: 0.5rem;
    animation: fadeInOut 0.3s ease-in-out;
    box-shadow: var(--shadow);
  }
  &__icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  &__icon-container {
    padding: 1rem;
    border-radius: 0 0 1rem 1rem;
    position: relative;
    &--info {
      color: var(--color-blue-primary);
      background-color: var(--color-blue-secondary);
      &::before {
        background-color: var(--color-blue-secondary);
      }
      &::after {
        border-bottom: 0.5rem solid var(--color-info-primary);
      }
    }
    &--success {
      color: var(--color-green-primary);
      background-color: var(--color-green-secondary);
      &::before {
        background-color: var(--color-green-secondary);
      }
      &::after {
        border-bottom: 0.5rem solid var(--color-green-primary);
      }
    }
    &--error {
      color: var(--color-red-primary);
      background-color: var(--color-red-secondary);
      &::before {
        background-color: var(--color-red-secondary);
      }
      &::after {
        border-bottom: 0.5rem solid var(--color-red-primary);
      }
    }
    &--warning {
      color: var(--color-yellow-primary);
      background-color: var(--color-yellow-secondary);
      &::before {
        background-color: var(--color-yellow-secondary);
      }
      &::after {
        border-bottom: 0.5rem solid var(--color-yellow-primary);
      }
    }
    &::before {
      content: "";
      position: absolute;
      top: -1rem;
      right: 0;
      height: 1rem;
      width: 100%;
    }
    &::after {
      content: "";
      position: absolute;
      right: 0;
      top: 0.2rem;
      transform: translateX(100%) translateY(calc(-100% - 0.65rem));
      border-left: 0 solid transparent;
      border-right: 0.3rem solid transparent;
    }
  }
  &__content {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
    justify-content: center;
    max-width: 40rem;
    min-width: 15rem;
  }
  &__message,
  &__title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &__close-btn {
    margin-left: 4rem;
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0.5;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
