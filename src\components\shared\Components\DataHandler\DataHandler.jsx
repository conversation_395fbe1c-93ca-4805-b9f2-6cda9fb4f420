import React, { useEffect, useState } from "react";

const DataHandler = ({
  loading,
  data,
  emptyComponent: EmptyComponent,
  loadingComponent: LoadingComponent,
  children,
}) => {
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    setInitialized(true);
  }, []);
  if (!initialized || (loading && (!data || data.length === 0))) {
    return <LoadingComponent />;
  }

  if (!loading && (!data || data.length === 0)) {
    return <EmptyComponent />;
  }
  return (
    <>
      {children} {loading && <LoadingComponent />}
    </>
  );
};

export default DataHandler;
