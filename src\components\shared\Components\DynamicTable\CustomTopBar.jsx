import {
  MRT_GlobalFilterT<PERSON><PERSON><PERSON><PERSON>,
  MRT_ShowHideColumnsButton,
  MRT_ToggleDensePaddingButton,
  MRT_ToggleFiltersButton,
  MRT_ToggleGlobalFilterButton,
} from "material-react-table";
import useQueryParam from "../../../../utilities/hooks/useQueryparam";
import "./DynamicTable.scss";

const CustomTopBar = ({
  table,
  ignoreViewType = false,
  customMenuComponent,
  children,
}) => {
  const viewType = useQueryParam("view");
  return (
    <>
      <section className="dynamic-table-topbar">
        {customMenuComponent && (
          <div className="dynamic-table-topbar__custom-component">
            {customMenuComponent}
          </div>
        )}
        <div className="dynamic-table-topbar__menu">
          <div className="dynamic-table-topbar__search-container">
            <MRT_GlobalFilterTextField table={table} />
            <MRT_ToggleGlobalFilterButton table={table} />
          </div>
          {(viewType === "table" || ignoreViewType) && (
            <>
              <MRT_ToggleFiltersButton table={table} />
              <MRT_ShowHideColumnsButton table={table} />
              <MRT_ToggleDensePaddingButton table={table} />
            </>
          )}
        </div>
        {children}
      </section>
    </>
  );
};

export default CustomTopBar;
