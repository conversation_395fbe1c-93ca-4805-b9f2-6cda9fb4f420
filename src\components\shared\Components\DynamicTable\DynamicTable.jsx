import React, { useRef } from "react";
import "./DynamicTable.scss";
import { MaterialReactTable } from "material-react-table";
import useDynamicTable from "../../../../utilities/hooks/useDynamicTable";

const DynamicTable = (props) => {
  const { table } = useDynamicTable(props);

  return (
    <section className='dynamic-table'>
      <MaterialReactTable table={table} />
    </section>
  );
};

export default DynamicTable;
