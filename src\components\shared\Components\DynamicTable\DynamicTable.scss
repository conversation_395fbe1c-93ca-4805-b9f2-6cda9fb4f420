.dynamic-table {
  flex: 1;
  overflow: auto;
  .MuiPaper-root {
    height: 100%;
    .css-zrlv9q {
      padding: 2px;
    }
  }
  .css-110vr2a-MuiInputBase-input-MuiInput-input {
    margin-left: 0.5rem;
  }
  .css-119b8ab {
    transform: translateX(-8px);
  }
  .css-1dt9vol {
    transform: translate(0px, 1px);
  }
  .css-lfttqv {
    transform: translateX(-15px);
  }
  .css-2tw9f8-MuiButtonBase-root-MuiIconButton-root,
  .css-ynq56y-MuiButtonBase-root-MuiIconButton-root {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .css-68rqdf {
    margin-left: 0.8rem;
  }

  // table {
  //   thead {
  //     th {
  //       border-bottom: 1rem solid var(--color-background);
  //       // border-top: 0.5rem solid var(--color-background);
  //     }
  //   }
  //   tbody {
  //     tr {
  //       td {
  //         border-bottom: 0.5rem solid var(--color-background);
  //       }
  //     }
  //   }
  // }
}
.table-icon {
  width: 2rem;
  height: 2rem;
  &__large {
    width: 2rem;
    height: 2rem;
    scale: 2.5;
  }
  &__draggable {
    width: 2rem;
    height: 2rem;
  }
}

.css-1v2kibc {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dynamic-table-topbar {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  position: relative;
  &__menu {
    display: flex;
    padding: 2px;
  }
  &__custom-component {
    margin-right: auto;
  }
  &__search-container {
    display: flex;
  }

  .css-rxgegx-MuiCollapse-root {
    position: absolute;
    top: 100%;
  }
}
