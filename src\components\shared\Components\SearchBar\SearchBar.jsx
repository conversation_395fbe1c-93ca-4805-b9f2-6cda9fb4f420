import React, { useRef, useEffect, useState } from "react";
import "./SearchBar.scss";
import ImportSvg from "../../importsvg/ImportSvg";

function SearchBar({ id = "", name = "", onChange }) {
  const [openSearch, setOpenSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState();
  const searchRef = useRef();

  useEffect(() => {
    function handleClickOutside(event) {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setOpenSearch(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleChange = (e) => {
    const { name, id, value } = e.target;
    const event = {
      name: name,
      id: id,
      value: value,
    };
    setSearchQuery(value);
    if (onChange) {
      onChange(event);
    }
  };

  return (
    <div className="search-bar" ref={searchRef}>
      {
        openSearch && (
        // <div
        //   className={`search-bar__input-container ${
        //     openSearch
        //       ? "search-bar__input-container--open"
        //       : "search-bar__input-container--close"
        //   }`}
        // >
        <div className='search-bar__input-container'>
          <ImportSvg icon="search-bar" className="search-bar__svg" />
          <input
            placeholder="Search"
            onChange={handleChange}
            id={id && id}
            name={name ? name : "search"}
            type="text"
            autoFocus
            value={searchQuery}
            className="search-bar__input"
          />
          <ImportSvg
            icon="close"
            onClick={() => setSearchQuery("")}
            className={`search-bar__cancel-svg ${
              searchQuery && "search-bar__cancel-svg--focus"
            }`}
          />
        </div>
        )
      }
      <button onClick={() => setOpenSearch((prev) => !prev)} className="search-bar__btn">
          <ImportSvg
            icon={openSearch ? "search-bar" : "search-bar"}
            className="search-bar__svg"
          />
      </button>

    </div>
  );
}

export default SearchBar;
