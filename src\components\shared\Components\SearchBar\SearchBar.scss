.search-bar {
    display: flex;
    align-items: center;
    min-height: 3rem;
    gap: 1rem;
    
    &__input-container {
        border-radius: 0.5rem;
        min-width: 0;
        padding: 0.5rem 1.5rem;
        align-items: center;
        gap: 1rem;
        border: 1px solid var(--color-primary);
        display: flex;
        transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    }
    &__svg {
        width: 1.5rem;
        height: 1.5rem;
        color: var(--color-primary);
        cursor: pointer;
    }
    &__cancel-svg {
        width: 2rem;

        height: 2rem;
        color: var(--color-text-secondary);
        cursor: pointer;
        transition: color 0.2s ease-in-out;
        &--focus {
            color: var(--color-primary);
        }
    }
    &__input {
        border: none;
        outline: none;
        color: var(--color-text);
        flex: 1 1;
        font-size: 1.2rem;
        background-color: transparent;
        max-width: 12rem;
    }
    &__container{
        position: relative;
        display: flex;
        background-color: var(--color-background);
        z-index: 2;
        padding: 0rem;
    }
    &__btn{
        padding: .5rem;

    }
}