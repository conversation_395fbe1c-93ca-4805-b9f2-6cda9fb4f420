import React from 'react';
import './StatusBarComponent.scss';

function StatusBarComponent({status}) {

    function capitalizeFirstCharacter(str) {
        if (!str) return str;
        return str.charAt(0).toUpperCase() + str.slice(1);
        }

  return (
    <div className={`status-bar status-bar--${status && status.toLowerCase()}`}>
      {capitalizeFirstCharacter(status)}
    </div>
  )
}

export default StatusBarComponent;
