import React from "react";
import ImportSvg from "../../importsvg/ImportSvg";
import "./TableActionField.scss";

function TableActionField({
  view,
  edit,
  close,
  handleEdit,
  handleClose,
  handleView,
}) {
  return (
    <div className='table-action-field'>
      {(view === true || view === false) && (
        <ImportSvg
          icon='view'
          onClick={() => handleView()}
          className={`table-action-field__svg ${
            !view && "table-action-field__svg--disable"
          }`}
        />
      )}
      {(edit === true || edit === false) && (
        <ImportSvg
          icon='edit'
          onClick={() => handleEdit()}
          className={`table-action-field__svg ${
            !edit && "table-action-field__svg--disable"
          }`}
        />
      )}
      {(close === true || close === false) && (
        <ImportSvg
          icon='close-icon'
          onClick={() => handleClose()}
          className={`table-action-field__svg ${
            !close && "table-action-field__svg--disable"
          }`}
        />
      )}
    </div>
  );
}

export default TableActionField;
