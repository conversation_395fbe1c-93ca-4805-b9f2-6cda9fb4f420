import React from "react";
import "./ToggleView.scss";
import ImportSvg from "../../importsvg/ImportSvg";

function ColumnView({ disabled, currentView, handleToggle }) {
  return (
    <div className="toggle-view">
      <button
        className="toggle-view__button"
        disabled={disabled}
        onClick={() => handleToggle && handleToggle("single")}
      >
        <ImportSvg
          icon="list-view-icon"
          className={`toggle-view__svg toggle-view__svg--table ${
            currentView && currentView.toLowerCase() === "single"
              ? `toggle-view__svg--active`
              : "toggle-view__svg--inactive"
          }`}
        />
      </button>
      <div className={`toggle-view__line`}></div>
      <button
        className="toggle-view__button"
        disabled={disabled}
        onClick={() => handleToggle && handleToggle("double")}
      >
        <ImportSvg
          icon="grid-view-icon"
          className={`toggle-view__svg  toggle-view__svg--table ${
            currentView && currentView.toLowerCase() === "double"
              ? "toggle-view__svg--active"
              : "toggle-view__svg--inactive"
          }`}
        />
      </button>
    </div>
  );
}

export default ColumnView;
