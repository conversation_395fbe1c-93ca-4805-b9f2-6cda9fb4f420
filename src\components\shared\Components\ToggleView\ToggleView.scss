.toggle-view{
    display: flex;
    align-items: center;
    border-radius: .5rem;
    border: 1px solid var(--color-stroke);
    border-radius: 0.5rem;
    width: min-content;
    height: min-content;
    background-color: var(--color-extreme-2);
    &__button{
        display: flex;
    }
    &__button:disabled > .toggle-view__svg{
        cursor: not-allowed;
    }
    &__svg{
        cursor: pointer;
        &--table{
            margin: 0.1rem .5rem;
            width: 2rem;
            height: 2rem;
        }
        &--view{
            margin: 0.5rem;
            width: 2rem;
            height: 2rem;
        }
        &--active{
            color: var(--color-primary);
        }
        &--inactive{
            color: var(--color-stroke);
        }
    }
    &__line{
        width: .2rem;
        background-color: var(--color-stroke);
        height: 2rem;
    }
}