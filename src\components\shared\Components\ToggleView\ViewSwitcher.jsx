import React, { useEffect } from "react";
import "./ToggleView.scss";
import ImportSvg from "../../importsvg/ImportSvg";
import { useNavigate } from "react-router-dom";
import useQueryParam from "../../../../utilities/hooks/useQueryparam";

const ViewSwitcher = ({ gridView, tableView, listView }) => {
  const navigate = useNavigate();
  const viewType = useQueryParam("view");

  const displaySvg = {
    grid: listView ? (!gridView && !tableView ? true : gridView) : true,
    table: listView ? tableView : true,
    list: listView,
  };

  const firstAvailableView = Object.entries(displaySvg).find(
    ([_, isEnabled]) => isEnabled
  )?.[0];

  useEffect(() => {
    if (!viewType && firstAvailableView) {
      navigate(`?view=${firstAvailableView}`, { replace: true });
    }
  }, [viewType, firstAvailableView, navigate]);

  const handleToggle = (view) => navigate(`?view=${view}`);

  const views = [
    { key: "grid", icon: "grid-view", condition: displaySvg.grid },
    { key: "list", icon: "list-view", condition: displaySvg.list },
    { key: "table", icon: "table-view", condition: displaySvg.table },
  ].filter((view) => view.condition);

  return (
    <div className='toggle-view'>
      {views.map((view, index) => (
        <React.Fragment key={view.key}>
          <button
            className='toggle-view__button'
            onClick={() => handleToggle(view.key)}>
            <ImportSvg
              icon={view.icon}
              className={`toggle-view__svg toggle-view__svg--view ${
                viewType === view.key
                  ? "toggle-view__svg--active"
                  : "toggle-view__svg--inactive"
              }`}
            />
          </button>
          {index < views.length - 1 && (
            <div className='toggle-view__line'></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default ViewSwitcher;
