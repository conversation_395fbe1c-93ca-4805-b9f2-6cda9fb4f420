import React from "react";
import "./DynamicForm.scss";
import SlidingFormWrapper from "../../shared/modalComponents/SlidingFormWrapper/SlidingFormWrapper";
import TextInput from "../../shared/Input Components/textinput/TextInput";
import useFormData from "../../../utilities/hooks/useFormData";
import CustomDropDown from "../Input Components/DropDown/CustomDropDown";
import { payload } from "../../../utilities/payload";
import useApiHandler from "../../../utilities/hooks/useApiHandler";
import { userRegister } from "../../../store/actions/usersAction";
import { useToast } from "../../../utilities/hooks/useToast";
import { leadCreation } from "../../../store/actions/leadsAction";

const DynamicForm = ({ displayForm, closeForm, formType, relevantFields }) => {
  const { formData, setFormData, handleChange } = useFormData(relevantFields);
  const [error, setError] = React.useState([]);
  const callApi = useApiHandler();
  const toast = useToast();
  const content = {
    user: {
      title: "Register User",
      sub_title: "Set up a new user profile with role based access",
      payload: payload.userRegister,
    },
    lead: {
      title: "Add Lead",
      sub_title:
        "Enter lead information to begin tracking potential opportunities",
      payload: payload.leadCreation,
    },
  };
  React.useEffect(() => {
    if (relevantFields?.length) {
      const updated = relevantFields.map((section) => ({
        ...section,
        fields: section.fields?.map((field) => ({
          ...field,
          infoMsg: "",
          infoType: "",
        })),
      }));
      setError(updated);
    }
  }, [relevantFields]);
  const handleSubmit = (e) => {
    e.preventDefault();
    const updatedErrors = error.map((section, sectionIndex) => {
      return {
        ...section,
        fields: section.fields.map((field, fieldIndex) => {
          const formFieldValue =
            formData?.[sectionIndex]?.fields?.[fieldIndex]?.value;
          if (
            field.selected &&
            field.required &&
            (formFieldValue === undefined ||
              formFieldValue === null ||
              (typeof formFieldValue === "string" &&
                formFieldValue.trim() === ""))
          ) {
            return {
              ...field,
              infoMsg: "This field is required",
              infoType: "error",
            };
          } else {
            return {
              ...field,
              infoMsg: "",
              infoType: "",
            };
          }
        }),
      };
    });

    setError(updatedErrors);
    if (
      updatedErrors?.some((section) =>
        section.fields.some((field) => field.infoType === "error")
      )
    ) {
      toast(
        "Failed to submit form",
        "Please fill in all required fields",
        "error"
      );
      return;
    }
    if (formType === "user") {
      const payload = {
        ...content.user.payload,
        user_fields: JSON.stringify(formData),
      };
      callApi(
        userRegister,
        payload,
        (res) => {
          toast("User registered successfully", res.message);
          setFormData(relevantFields);
          closeForm();
        },
        (err) => toast("Failed to register user", err, "error")
      );
    } else if (formType === "lead") {
      const payload = {
        ...content.lead.payload,
        lead_fields: JSON.stringify(formData),
      };
      callApi(
        leadCreation,
        payload,
        (res) => {
          toast("Lead created successfully", res.message);
          setFormData(relevantFields);
          closeForm();
        },
        (err) => toast("Failed to create lead", err, "error")
      );
    }
  };

  return (
    <>
      {relevantFields && (
        <SlidingFormWrapper displayForm={displayForm} closeForm={closeForm}>
          <form className="dynamic-form" onSubmit={handleSubmit}>
            <div className="dynamic-form__header">
              <div className="dynamic-form__header-content">
                <h1>{content[formType].title}</h1>
                <p>{content[formType].sub_title}</p>
              </div>
              <div className="dynamic-form__header-buttons">
                <button
                  className="btn btn__secondary"
                  onClick={closeForm}
                  type="button"
                >
                  Cancel
                </button>
                <button className="btn btn__primary" type="submit">
                  Save
                </button>
              </div>
            </div>
            <div className="dynamic-form__content">
              {relevantFields?.map((section, sectionIndex) => (
                <div
                  className="dynamic-form__section"
                  key={`preview-sectionIndex-${sectionIndex}`}
                >
                  <h5>{section.section_name}</h5>
                  <div className="dynamic-form__section-content">
                    {(() => {
                      const filteredFields =
                        section.fields?.filter(
                          (field) => field.selected || field.required
                        ) || [];

                      const groupedFields = [];
                      for (let i = 0; i < filteredFields.length; i += 2) {
                        groupedFields.push(filteredFields.slice(i, i + 2));
                      }

                      return groupedFields.map((group, groupIndex) => {
                        return (
                          <div
                            className={`dynamic-form__section-field-group ${
                              section.column_type?.toLowerCase() === "double"
                                ? "dynamic-form__section-field-group--double"
                                : ""
                            }`}
                            key={`field-group-${sectionIndex}-${groupIndex}`}
                          >
                            {group.map((field, fieldIndexInGroup) => {
                              const actualFieldIndex =
                                groupIndex * 2 + fieldIndexInGroup;
                              const textFieldTypes = [
                                "text",
                                "email",
                                "password",
                                "phone",
                              ];
                              const selectTypes = ["select", "multi-select"];
                              if (
                                textFieldTypes.includes(
                                  field.type?.toLowerCase()
                                )
                              ) {
                                return (
                                  <TextInput
                                    key={`preview-field-${sectionIndex}-${actualFieldIndex}`}
                                    label={field.label}
                                    placeholder={field.placeholder}
                                    value={
                                      formData?.[sectionIndex]?.fields?.[
                                        actualFieldIndex
                                      ]?.value || ""
                                    }
                                    id={`${sectionIndex}.fields.${actualFieldIndex}.value`}
                                    onChange={(e) => {
                                      setError((prev) => {
                                        const updated = [...prev];
                                        updated[sectionIndex].fields[
                                          actualFieldIndex
                                        ].infoMsg = "";
                                        updated[sectionIndex].fields[
                                          actualFieldIndex
                                        ].infoType = "";
                                        return updated;
                                      });
                                      handleChange(e);
                                    }}
                                    infoType={
                                      error?.[sectionIndex]?.fields?.[
                                        actualFieldIndex
                                      ]?.infoType
                                    }
                                    infoMsg={
                                      error?.[sectionIndex]?.fields?.[
                                        actualFieldIndex
                                      ]?.infoMsg
                                    }
                                  />
                                );
                              } else if (
                                selectTypes.includes(field.type?.toLowerCase())
                              ) {
                                return (
                                  <CustomDropDown
                                    variant="small"
                                    key={`preview-field-${sectionIndex}-${actualFieldIndex}`}
                                    options={field.options.map(
                                      (option) =>
                                        option.role_name || option.name
                                    )}
                                    label={field.label}
                                    relevantOptions={field.options.map(
                                      (option) => option.role_id || option.id
                                    )}
                                    value={
                                      formData?.[sectionIndex]?.fields?.[
                                        actualFieldIndex
                                      ]?.value || ""
                                    }
                                    id={`${sectionIndex}.fields.${actualFieldIndex}.value`}
                                    onChange={(e) => {
                                      handleChange(e);
                                    }}
                                  />
                                );
                              }
                            })}
                          </div>
                        );
                      });
                    })()}
                  </div>
                </div>
              ))}
            </div>
          </form>
        </SlidingFormWrapper>
      )}
    </>
  );
};

export default DynamicForm;
