.dynamic-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
  &__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 5rem;
  }
  &__header-content {
    display: flex;
    flex-direction: column;
    h1 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--color-text);
    }
    p {
      font-size: 1rem;
      color: var(--color-text-secondary);
    }
  }
  &__header-buttons {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    button {
      border-radius: 0.5rem;
    }
  }
  &__content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    color: var(--color-text);
  }
  &__section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  &__section-field-group {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    &:not(:first-child) {
      margin-top: 3rem;
    }
    &:first-child {
      margin-top: 1rem;
    }
    &--double {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 1.5rem;

      > * {
        flex: 0 0 calc(50% - 0.75rem);
        max-width: calc(50% - 0.75rem);
      }
    }
  }
}
