import React from "react";
import "./CustomCheckBox.scss";

function CustomCheckBox({
  id,
  onChange,
  value,
  disabled = false,
  size = "medium",
  label,
  className,
  ...props
}) {
  const handleChange = (e) => {
    if (onChange) {
      onChange(e);
    }
  };

  // function capitalizeFirstChar(str) {
  //   if (!str) return '';
  //   return str.charAt(0).toUpperCase() + str.slice(1);
  // }

  // const handleClick = () => {
  //   if(onChange && !disabled){
  //     onChange({ target: { checked: !value } });
  //   }
  // }

  const getClassName = (type) => {
    if (type === "input") {
      switch (size.toLowerCase()) {
        case "small":
          return "check-box__input check-box__input--small";
        case "medium":
          return "check-box__input check-box__input--medium";
        case "large":
          return "check-box__input check-box__input--large";
        default:
          return "check-box__input";
      }
    } else if (type === "label") {
      switch (size.toLowerCase()) {
        case "small":
          return "check-box__label-content--small";
        case "medium":
          return "check-box__label-content--medium";
        case "large":
          return "check-box__label-content--large";
        default:
          return "";
      }
    }
  };
  const getFontSize = () => {
    switch (size.toLowerCase()) {
      case "small":
        return "1rem";
      case "medium":
        return "1.5rem";
      case "large":
        return "2.5rem";
      default:
        return "1rem";
    }
  };
  return (
    <div className={`check-box ${className}`} {...props}>
      <input
        className={`${getClassName("input")} "check-box__input"`}
        checked={value}
        style={{ "--tick-size": getFontSize() }}
        type="checkbox"
        disabled={disabled}
        onChange={handleChange}
        id={id}
        onClick={(e) => e.stopPropagation()}
      />
      {label && (
        <label
          className={`${
            !className ? "check-box__label-content" : className
          } ${getClassName("label")} `}
          htmlFor={id}
        >
          {label}
        </label>
      )}
      {/* {label && <p className={`${!className ? 'check-box__label-content' : className} `} onClick={handleClick}>{capitalizeFirstChar(label)}</p>} */}
    </div>
  );
}

export default CustomCheckBox;
