.check-box {
  display: flex;
  flex: 1;
  cursor: pointer;
  align-items: center;
  // width: min-content;
  height: min-content;
  align-items: center;
  gap: 1rem;

  &__input {
    width: 1.5rem;
    height: 1.5rem;
    background-color: white;
    border: 1px solid var(--color-stroke);
    border-radius: 0.4rem;
    appearance: none;
    -webkit-appearance: none;
    position: relative;
    box-shadow: var(--shadow);
    &:checked::after {
      content: "\2714";
      font-size: var(--tick-size);
      font-weight: 700;
      color: white;
      position: absolute;
      // top: 0;
      // left: -10%;
      width: 100%;
      height: 100%;
      text-align: center;
      line-height: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &:checked {
      background-color: var(--color-primary);
      z-index: 0;
      border: none;
    }
    &--small {
      width: 1.25rem;
      height: 1.25rem;
      border-radius: 0.2rem;
    }
    &--medium {
      width: 1.75rem;
      height: 1.75rem;
    }
    &--large {
      width: 2.5rem;
      height: 2.5rem;
    }
  }
  &__label-content {
    // font-size: 1.5rem;
    flex: 1;
    color: var(--color-text);
    font-weight: 600;
    cursor: pointer;
    &--small {
      font-size: 1rem;
    }
    &--medium {
      font-size: 1.25rem;
    }
    &--large {
      font-size: 1.5rem;
    }
  }
}
