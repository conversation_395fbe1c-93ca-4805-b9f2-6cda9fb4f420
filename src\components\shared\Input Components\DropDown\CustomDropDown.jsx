import React, { useState, useRef, useEffect } from "react";
import "./CustomDropDown.scss";
import ImportSvg from "../../importsvg/ImportSvg";
import SearchingInputComp from "../SearchingInputComp/SearchingInputComp";

function CustomDropDown({
  variant = "medium",
  options,
  relevantOptions,
  onChange,
  name = "",
  id = "",
  className,
  label,
  value,
  type = "normal",
}) {
  const [openDropdown, setDropdown] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value);
  const [selectedIndex, setSelectedIndex] = useState();
  const [searchQuery, setSearchQuery] = useState();
  const [filteredOption, setFilteredOption] = useState([]);
  const dropdownRef = useRef(null);
  const handleOptionSelection = (value, index) => {
    setDropdown(false);
    setSelectedValue(value);
    if (type === "colored") {
      setSelectedIndex(index % 5);
    }
    const event = {
      target: {
        name: name,
        id: id,
        value: relevantOptions ? relevantOptions[index] : value,
      },
    };
    if (onChange) {
      onChange(event);
    }
  };

  useEffect(() => {
    setSelectedValue(value);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setFilteredOption(() => {
      return options?.filter(
        (val) =>
          typeof val === "string" &&
          typeof searchQuery === "string" &&
          val.toLowerCase().includes(searchQuery.toLowerCase())
      );
    });
  }, [searchQuery, options]);

  const getClassName = (index) => {
    if (type === "colored") {
      switch (index % 5) {
        case 0:
          return "custom-dropdown__option--0";
        case 1:
          return "custom-dropdown__option--1";
        case 2:
          return "custom-dropdown__option--2";
        case 3:
          return "custom-dropdown__option--3";
        case 4:
          return "custom-dropdown__option--4";
        default:
          return;
      }
    }
  };
  const getClass = () => {
    if (type === "colored") {
      switch (selectedIndex % 5) {
        case 0:
          return "custom-dropdown__selected-option--0";
        case 1:
          return "custom-dropdown__selected-option--1";
        case 2:
          return "custom-dropdown__selected-option--2";
        case 3:
          return "custom-dropdown__selected-option--3";
        case 4:
          return "custom-dropdown__selected-option--4";
        default:
          return;
      }
    }
  };

  return (
    <div
      ref={dropdownRef}
      className={`custom-dropdown${className ? ` ${className}` : ""}${
        selectedValue && type !== "colored" ? " custom-dropdown--selected" : ""
      } custom-dropdown--${variant.toLowerCase()}`}
      onClick={() => {
        setDropdown((prev) => !prev);
      }}
      tabIndex={0}
    >
      <div className="custom-dropdown__selected-option-container">
        <p
          className={`custom-dropdown__selected-option ${getClass()}`}
          // style={type === 'colored' ? { '--border': borderColors[selectedIndex % 5], '--bg-color': backgroundColors[selectedIndex % 5], borderRadius: '2rem' } : undefined}
        >
          {!selectedValue ? (
            "Choose"
          ) : (
            <span className="custom-dropdown__selected-option--text">
              {relevantOptions
                ? options[relevantOptions.indexOf(selectedValue)]
                  ? options[relevantOptions.indexOf(selectedValue)]
                  : selectedValue
                : selectedValue}
            </span>
          )}
        </p>
      </div>
      <ImportSvg
        className={`custom-dropdown__svg ${
          openDropdown && "custom-dropdown__svg--flip"
        }`}
        icon="down-arrow"
      />
      {openDropdown && (
        <div
          className={`custom-dropdown__options-list ${
            type === "colored" && "custom-dropdown__options-list--colored"
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <SearchingInputComp
            searchQuery={searchQuery}
            setSearchQuery={(value) => setSearchQuery(value)}
          />
          {options &&
            (searchQuery && filteredOption.length === 0 ? (
              <div className="custom-dropdown__no-results">
                No results found
              </div>
            ) : (
              (searchQuery ? filteredOption : options).map((val, idx) => (
                <div
                  key={idx}
                  className={`custom-dropdown__option-container ${
                    type === "colored"
                      ? "custom-dropdown__option-container--colored"
                      : "custom-dropdown__option-container--normal"
                  }`}
                >
                  <p
                    onClick={() => handleOptionSelection(val, idx)}
                    className={`custom-dropdown__option ${getClassName(idx)} ${
                      variant?.toLowerCase() === "large"
                        ? "custom-dropdown__option--large"
                        : ""
                    }`}
                  >
                    {val}
                  </p>
                </div>
              ))
            ))}
        </div>
      )}
      {label && <p className="custom-dropdown__label">{label}</p>}
    </div>
  );
}

export default CustomDropDown;
