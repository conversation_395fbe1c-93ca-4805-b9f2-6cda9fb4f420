.custom-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.6rem 1.2rem;
  position: relative;
  border: 1px solid var(--color-stroke);
  background-color: var(--color-extreme-2);
  border-radius: 0.2rem;
  min-width: 18rem;
  cursor: pointer;
  &--large {
    padding: 1.2rem 1.5rem;
  }
  &--small {
    padding: 0.2rem 0.6rem;
  }
  &--selected {
    background-color: var(--color-secondary);
  }

  &__label {
    font-size: 1rem;
    font-weight: 600;
    display: block;
    transition: all 0.3s;
    position: absolute;
    top: -35%;
    left: 0;
    transform: translateY(-50%);
    white-space: nowrap;
  }
  &__selected-option-container {
    flex: 1 1;
    width: 100%;
    overflow: hidden;
  }
  &__selected-option {
    color: var(--color-text-secondary);
    font-size: inherit;
    font-weight: inherit;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: min-content;
    max-width: 100%;
    padding: 0.3rem 0.5rem;
    border-radius: 2rem;
    &--text {
      color: var(--color-text);
    }
    &--0 {
      background-color: #f5e8ff;
      border: 1px solid var(--color-primary);
    }
    &--1 {
      background-color: var(--color-blue-secondary);
      border: 1px solid var(--color-blue-primary);
    }
    &--2 {
      background-color: var(--color-green-secondary);
      border: 1px solid var(--color-green-primary);
    }
    &--3 {
      background-color: var(--color-yellow-secondary);
      border: 1px solid var(--color-yellow-primary);
    }
    &--4 {
      background-color: var(--color-red-secondary);
      border: 1px solid var(--color-red-primary);
    }
  }
  &__options-list {
    display: flex;
    flex-direction: column;
    border: 1px solid var(--color-stroke);
    border-top: none;
    position: absolute;
    top: calc(100% + 0.5rem);
    width: 100%;
    left: 0;
    padding: 1rem;
    gap: 0.8rem;
    transition: all 0.5s ease-out;
    background-color: var(--color-extreme-2);
    border-radius: 0.5rem;
    z-index: 10;
    max-height: 15rem;
    overflow-y: auto;
    overflow-x: hidden;
    &--colored {
      gap: 1rem;
      padding: 1rem;
    }
  }
  &__option-container {
    &:first-child {
      border-top-left-radius: 0.5rem;
      border-top-right-radius: 0.5rem;
    }
    &:last-child {
      border-bottom-left-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
    }
    &--normal {
      border: 1px solid transparent;
      &:hover {
        background-color: var(--color-secondary);
        p {
          color: var(--color-text);
        }
      }
      &:active {
        border: 1px solid var(--color-primary);
      }
    }
  }

  &__option {
    background-color: transparent;
    color: var(--color-text-secondary);
    padding: 0.3rem 0.8rem;
    font-size: inherit;
    font-weight: inherit;
    width: 100%;
    height: fit-content;
    white-space: nowrap;
    border: 1px solid var(--border);
    overflow: hidden;
    background-color: var(--bg-color);
    text-overflow: ellipsis;

    &--large {
      padding: 0.6rem 1.2rem;
    }
    &--0 {
      background-color: #f5e8ff;
      border: 1px solid var(--color-primary);
      border-radius: 2rem;
      width: min-content;
      max-width: 100%;
    }
    &--1 {
      background-color: var(--color-blue-secondary);
      border: 1px solid var(--color-blue-primary);
      border-radius: 2rem;
      max-width: 100%;
      width: min-content;
    }
    &--2 {
      background-color: var(--color-green-secondary);
      border: 1px solid var(--color-green-primary);
      border-radius: 2rem;
      max-width: 100%;
      width: min-content;
    }
    &--3 {
      background-color: var(--color-yellow-secondary);
      border: 1px solid var(--color-yellow-primary);
      border-radius: 2rem;
      max-width: 100%;
      width: min-content;
    }
    &--4 {
      background-color: var(--color-red-secondary);
      border: 1px solid var(--color-red-primary);
      border-radius: 2rem;
      max-width: 100%;
      width: min-content;
    }
  }
  &__no-results {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text);
  }
  &__svg {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--color-primary);
    transition: 0.3s;
    &--flip {
      transform: rotate(180deg);
      transition: 0.3s;
    }
  }
}
