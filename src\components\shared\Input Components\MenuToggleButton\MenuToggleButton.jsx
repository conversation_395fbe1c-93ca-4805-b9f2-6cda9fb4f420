import React from "react";
import "./MenuToggleButton.scss";

const MenuToggleButton = ({ menu, count, selected, setSelected }) => {
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  return (
    <div
      className="menu-toggle"
      style={{
        "--item-count": `calc(${menu?.length} + 1)`,
        "--index-offset": `calc(${selectedIndex} * 100%)`,
      }}
    >
      {menu?.map((item, index) => {
        return (
          <div
            key={index}
            onClick={() => {
              setSelected(item);
              setSelectedIndex(index);
            }}
            className={`menu-toggle__item ${
              selectedIndex === index ? "menu-toggle__item--active" : ""
            }`}
          >
            <p className="menu-toggle__title">{item}</p>
            {count[index] && (
              <div
                className={`menu-toggle__count ${
                  selectedIndex === index ? "menu-toggle__count--active" : ""
                }`}
              >
                {count[index]}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default MenuToggleButton;
