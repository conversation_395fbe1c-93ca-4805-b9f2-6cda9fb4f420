.menu-toggle {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: var(--color-extreme-2);
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  position: relative;
  z-index: 1;
  flex: 1;
  &::before {
    content: "";
    position: absolute;
    top: 0.25rem;
    left: 0.5rem;
    height: calc(100% - 0.5rem);
    background-color: var(--color-secondary);
    border-radius: 0.5rem;
    z-index: -1;
    border: 1px solid var(--color-primary);
    /* STEP 1: Shrink each slot width by the total gap space */
    width: calc((100% - (var(--item-count) - 1) * 1rem) / var(--item-count));

    /* STEP 2: Slide by “100% × index + 1rem × index”, minus (1rem ÷ itemCount) per index */
    transform: translateX(
      calc(
        var(--translate-offset) -
          (var(--selected-index) * 1rem / var(--item-count))
      )
    );
    transition: transform 0.3s ease;
    pointer-events: none;
  }
  &__item {
    display: flex;
    width: calc((100% - (var(--item-count) - 1) * 1rem) / var(--item-count));
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
  }
  &__title {
    font-size: 1.2rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &__count {
    padding: 0rem 0.5rem;
    border-radius: 2px;
    background-color: var(--color-secondary);
    &--active {
      background-color: var(--color-extreme-2);
    }
  }
}
