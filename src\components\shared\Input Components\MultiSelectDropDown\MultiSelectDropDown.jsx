// import React from 'react'
import "./MultiSelectDropDown.scss";

import React, { useState, useRef, useEffect } from "react";
import ImportSvg from "../../importsvg/ImportSvg";
import SearchingInputComp from "../SearchingInputComp/SearchingInputComp";
import CustomCheckBox from "../CustomChechBox/CustomCheckBox";

function MultiSelectDropDown({
  variant = "medium",
  options = [],
  onChange,
  name = "",
  id = "",
  className,
  label,
  value = [],
  type = "normal",
}) {
  const [openDropdown, setDropdown] = useState(false);
  const [selectedValues, setSelectedValues] = useState(value || []);

  //   const [selectedIndex, setSelectedIndex] = useState();

  const [searchQuery, setSearchQuery] = useState();
  const [filteredOption, setFilteredOption] = useState([]);
  const dropdownRef = useRef(null);
  const handleOptionSelection = (option) => {
    setSelectedValues((prev) => {
      const isSelected = prev.includes(option);
      const updatedSelection = isSelected
        ? prev.filter((val) => val !== option) // Remove if already selected
        : [...prev, option]; // Add if not selected

      if (onChange) {
        onChange({
          target: { name, id, value: updatedSelection },
        });
      }
      return updatedSelection;
    });
  };

  const handleRemoveSelection = (option) => {
    setSelectedValues((prev) => prev.filter((val) => val !== option));

    if (onChange) {
      onChange({
        target: {
          name,
          id,
          value: selectedValues.filter((val) => val !== option),
        },
      });
    }
  };

  useEffect(() => {
    setSelectedValues(value);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setFilteredOption(
      options.filter(
        (val) =>
          typeof val === "string" &&
          searchQuery &&
          typeof searchQuery === "string" &&
          val.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
  }, [searchQuery, options]);

  const getClassName = (index) => {
    if (type === "colored") {
      switch (index % 5) {
        case 0:
          return "multi-select-drop-down__option--0";
        case 1:
          return "multi-select-drop-down__option--1";
        case 2:
          return "multi-select-drop-down__option--2";
        case 3:
          return "multi-select-drop-down__option--3";
        case 4:
          return "multi-select-drop-down__option--4";
        default:
          return;
      }
    }
  };

  const firstSelectedIndex =
    selectedValues.length > 0 ? options.indexOf(selectedValues[0]) : -1;

  const getClass = (index) => {
    if (type === "colored" && index >= 0) {
      return `multi-select-drop-down__selected-option--${index % 5}`;
    }
    return "multi-select-drop-down__selected-option--normal"; // Default class if no selection
  };

  return (
    <div
      ref={dropdownRef}
      className={`multi-select-drop-down ${className && className} ${
        selectedValues && "multi-select-drop-down--selected"
      } ${
        variant.toLowerCase() === "large" && "multi-select-drop-down--large"
      }`}
      onClick={() => setDropdown((prev) => !prev)}
    >
      <div className="multi-select-drop-down__selected-option-container">
        {selectedValues.length === 0 ? (
          <p className="multi-select-drop-down__selected-option">Choose</p>
        ) : (
          selectedValues.map((val, idx) => {
            const selectedIndex = options.indexOf(val); // Get the correct index of the value in the options list
            return (
              <p
                key={idx}
                className={`${
                  type === "colored"
                    ? `multi-select-drop-down__selected-option ${getClass(
                        selectedIndex
                      )}`
                    : `multi-select-drop-down__selected-option--normal`
                }`}
              >
                <span className="multi-select-drop-down__selected-item">
                  {val}
                  <button
                    className={`${type === "colored" ? `multi-select-drop-down__remove-btn--${
                      selectedIndex % 5
                    }` :"multi-select-drop-down__remove-btn--normal"}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveSelection(val);
                    }}
                  >
                    ✖
                  </button>
                </span>
              </p>
            );
          })
        )}
      </div>

      <ImportSvg
        className={`multi-select-drop-down__svg ${
          openDropdown && "multi-select-drop-down__svg--flip"
        }`}
        icon="down-arrow"
      />
      {openDropdown && (
        <div
          className={`multi-select-drop-down__options-list ${
            type === "colored" &&
            "multi-select-drop-down__options-list--colored"
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <SearchingInputComp
            searchQuery={searchQuery}
            setSearchQuery={(value) => setSearchQuery(value)}
          />
          {options &&
            (searchQuery && filteredOption.length === 0 ? (
              <div className="multi-select-drop-down__no-results">
                No results found
              </div>
            ) : (
              (searchQuery ? filteredOption : options).map((val, idx) => (
                <div
                  key={idx}
                  className={`multi-select-drop-down__option-container ${
                    type === "colored"
                      ? "multi-select-drop-down__option-container--colored"
                      : `${
                          selectedValues.includes(val)
                            ? "multi-select-drop-down__option-container--selected"
                            : ""
                        }`
                  }`}
                  onClick={() => handleOptionSelection(val, idx)}
                >
                  <p
                    className={`multi-select-drop-down__option ${getClassName(
                      idx
                    )} ${
                      variant?.toLowerCase() === "large"
                        ? "multi-select-drop-down__option--large"
                        : ""
                    }`}
                  >
                    {val}
                    {type === "colored" && selectedValues.includes(val) && (
                      <CustomCheckBox
                        checked={selectedValues.includes(val)}
                        id={`${name}-${idx}`}
                        value={selectedValues.includes(val)}
                        className={`${
                          type === "colored"
                            ? `multi-select-drop-down__checkbox--${
                                options.indexOf(val) % 5
                              }`
                            : ""
                        }`}
                      />
                    )}
                  </p>
                  {type !== "colored" && (
                    <CustomCheckBox
                      checked={selectedValues?.includes(val)}
                      id={`${name}-${idx}`}
                      value={selectedValues.includes(val)}
                    />
                  )}
                </div>
              ))
            ))}
        </div>
      )}
      {label && <p className="multi-select-drop-down__label">{label}</p>}
    </div>
  );
}

export default MultiSelectDropDown;
