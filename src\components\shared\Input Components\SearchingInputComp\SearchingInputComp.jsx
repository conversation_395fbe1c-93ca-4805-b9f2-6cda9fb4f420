import React from "react";
import "./SearchingInputComp.scss";
import TextInput from "../textinput/TextInput";
// import useFormData from '../../../../utilities/hooks/useFormData';
import ImportSvg from "../../importsvg/ImportSvg";

function SearchingInputComp({ setSearchQuery, searchQuery }) {
  return (
    <div className="searching-input-comp">
      <TextInput
        id="searchOption"
        onChange={(e) => setSearchQuery(e.target.value)}
        value={searchQuery}
        placeholder="Search Here..."
      />
      <ImportSvg icon="search-bar" className="searching-input-comp__svg" />
    </div>
  );
}

export default SearchingInputComp;
