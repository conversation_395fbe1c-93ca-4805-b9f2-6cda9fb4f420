.searching-input-comp {
  display: flex;
  align-items: center;
  width: 100%;
  // position: relative;
  position: sticky;
  z-index: 1;
  top: 0;
  & > .text-input > .text-input__input:focus {
    border-bottom: 2px solid transparent;
    box-shadow: none;
  }
  &__input-field {
    width: 100%;
    min-height: 3rem;
    border: 1px solid var(--color-stroke);
    border-radius: 0.3rem;
    color: var(--color-text-secondary);
    padding-left: 1rem;
    font-size: inherit;
    &::placeholder {
      color: var(--color-text-secondary);
      font-size: inherit;
    }
  }
  &__svg {
    width: 1.2rem;
    height: 1.2rem;
    position: absolute;
    top: 30%;
    right: 1rem;
  }
}
