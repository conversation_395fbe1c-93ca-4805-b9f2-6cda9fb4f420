import React, { useEffect, useState } from "react";
import "./ToggleContainer.scss";
import ToggleButton from "../ToogleButton/ToggleButton";

const ToggleContainer = ({
  data,
  handleToggleStateChange,
  border,
  toggleButtonSize = "small",
  borderStyle = {},
}) => {
  const [toggleState, setToggleState] = useState(data);

  const defaultBorderStyle = {
    // height: "auto",
    overflowY: "visible",
    // padding: "1rem 1rem",
    padding: "1rem 0.5rem",
    borderRadius: "1rem",
    border: "1px solid #E5E5E5",
    ...borderStyle, // Allow overriding default styles
  };

  const lineStyle = {
    top: "4.1rem",
    height: "40%",
  };

  useEffect(() => {
    if (data) {
      setToggleState(data);
    }
  }, [data]);

  const handleMainModuleToggle = (moduleIndex) => {
    setToggleState((prevState) => {
      const updatedModules = [...prevState];
      const currentModule = updatedModules[moduleIndex];
      const newSelectedState = !currentModule.selected;

      updatedModules[moduleIndex] = {
        ...currentModule,
        selected: newSelectedState,
        submenu: currentModule.submenu.map((submenu) => ({
          ...submenu,
          selected: newSelectedState ? submenu.selected : false,
        })),
      };

      return updatedModules;
    });
  };

  useEffect(() => {
    handleToggleStateChange(toggleState);
  }, [toggleState]);

  const handleSubmenuToggle = (moduleIndex, submenuIndex) => {
    setToggleState((prevState) => {
      const updatedModules = [...prevState];
      const updatedSubmenu = [...updatedModules[moduleIndex].submenu];

      updatedSubmenu[submenuIndex] = {
        ...updatedSubmenu[submenuIndex],
        selected: !updatedSubmenu[submenuIndex].selected,
      };

      const isAnySubmenuSelected = updatedSubmenu.some((sub) => sub.selected);
      updatedModules[moduleIndex] = {
        ...updatedModules[moduleIndex],
        submenu: updatedSubmenu,
        selected: isAnySubmenuSelected,
      };

      return updatedModules;
    });
  };

  return (
    <div className="toggle-container__toggle_list-container">
      {toggleState.length > 0 &&
        toggleState.map((module, moduleIndex) => (
          <div
            className="toggle-container__toggle-list"
            key={module.module_name}
            style={border ? defaultBorderStyle : {}}
          >
            {/* Main Module Toggle */}
            <h3 className="toggle-container__toggle-list-title">
              <ToggleButton
                value={module.selected}
                onChange={() => handleMainModuleToggle(moduleIndex)}
                label={module.module_name}
                size={toggleButtonSize}
                className={"label"}
              />
            </h3>

            {/* Submenu Items */}
            <div className="toggle-container__sub-menu">
              {module.submenu.map((submenu, submenuIndex) => (
                <div
                  className="toggle-container__toggle-list-items"
                  key={`${module.module_name}-${submenuIndex}`}
                >
                  <ToggleButton
                    className={"toggle-container__label"}
                    value={submenu.selected}
                    onChange={() =>
                      handleSubmenuToggle(moduleIndex, submenuIndex)
                    }
                    label={submenu.sub_module_name}
                    size={toggleButtonSize}
                  />
                </div>
              ))}
            </div>
          </div>
        ))}
    </div>
  );
};

export default ToggleContainer;
