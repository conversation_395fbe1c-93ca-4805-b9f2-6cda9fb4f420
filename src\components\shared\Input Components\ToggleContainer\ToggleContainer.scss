.toggle-container {
  &__toggle-list-header {
    font-size: 1.4rem;
    padding-bottom: 0.3rem;
  }
  &__toggle-list-header-sub {
    margin-bottom: 1.2rem;
  }
  &__toggle_list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.4rem;
    height: 100%;
    overflow-y: auto;
    // scrollbar-width: none;
  }
  &__sub-menu{
    display: flex;
    flex-direction: column;
    gap: .7rem;
    margin-left: 2rem;
    padding: 0rem 1rem;
    position: relative;
    &::before{
      position: absolute;
      top: 0%;
      left: 0%;
      height: 100%;
      width: .1rem;
      background-color: #e5e5e5;
      content: '';
    }
  }
  &__toggle-list {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: .7rem;
  }

  &__toggle-list-title {
    font-weight: 500;
  }
  &__line {
    height: 70%;
    width: 0.1rem;
    top: 2.6rem;
    left: 2rem;
    position: absolute;
    background-color: #e5e5e5;
  }
  &__toggle-list-items {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
    // margin-top: 0.7rem;
    // margin-left: 2.5rem;
  }
  &__label {
    color: #7d7d7d;
    font-size: 1.2rem;
    text-wrap: none;
    white-space: nowrap;
  }
}
