import React from "react";
import "./ToggleButton.scss";

function ToggleButton({
  size = "medium",
  disabled = false,
  onChange = () => {},
  value,
  id,
  label,
  className,
}) {
  const handleChange = (e) => {
    if (onChange) {
      onChange(e);
    }
  };
  const inputId =
    id || `toggle-button-${Math.random().toString(36).substr(2, 9)}`;
  function capitalizeFirstChar(str) {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  const handleClick = () => {
    if (onChange && !disabled) {
      onChange({ target: { checked: !value } });
    }
  };

  const getClassName = () => {
    switch (size.toLowerCase()) {
      case "small":
        return "toggle-button__toogle-label--small";
      case "medium":
        return "toggle-button__toogle-label--medium";
      case "large":
        return "toggle-button__toogle-label--large";
      default:
        return "";
    }
  };

  return (
    <div className="toggle-button">
      <input
        type="checkbox"
        id={inputId}
        checked={value}
        onChange={handleChange}
        className={`toggle-button__input-field ${
          disabled && "toggle-button__input-field--disabled"
        }`}
        disabled={disabled}
      />
      <label
        htmlFor={inputId}
        className={`toggle-button__toogle-label ${getClassName()}`}
      ></label>
      {label && (
        <p
          className={`${
            !className ? "toggle-button__label-content" : className
          } `}
          onClick={handleClick}
        >
          {capitalizeFirstChar(label)}
        </p>
      )}
    </div>
  );
}

export default ToggleButton;
