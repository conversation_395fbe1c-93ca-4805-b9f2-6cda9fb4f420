.toggle-button {
  display: flex;
  align-items: center;
  width: min-content;
  height: min-content;
  gap: 1rem;
  &__input-field {
    display: none;
    opacity: 1;
    &:checked + .toggle-button__toogle-label {
      background-color: var(--color-primary);
    }
    &:checked + .toggle-button__toogle-label--medium::before {
      background-color: white;
      transform: translateX(1.95rem);
    }
    &:checked + .toggle-button__toogle-label--small::before {
      background-color: white;
      transform: translateX(1.5rem);
    }
    &:checked + .toggle-button__toogle-label--large::before {
      background-color: white;
      transform: translateX(2.45rem);
    }
    &:disabled + .toggle-button__toogle-label {
      opacity: 0.6;
    }
  }

  &__toogle-label {
    cursor: pointer;
    border-radius: 1.25rem;
    position: relative;
    transition: 0.2s;
    background-color: var(--color-stroke);
    &::before {
      position: absolute;
      content: "";
      background-color: white;
      border-radius: 50%;
      transition: 0.2s;
    }
    &--medium {
      width: 4rem;
      height: 2.1rem;
      &::before {
        width: 1.75rem;
        height: 1.75rem;
        margin: 0.15rem;
        margin-right: 0.2rem;
        margin-top: 0.15rem;
        margin-bottom: 0.25rem;
      }
    }
    &--small {
      width: 3rem;
      height: 1.55rem;
      &::before {
        width: 1.25rem;
        height: 1.25rem;
        margin: 0.1rem;
        // margin-right: .15rem;
        margin-top: 0.15rem;
        margin-left: 0.15rem;
      }
    }
    &--large {
      width: 5rem;
      height: 2.5rem;
      &::before {
        width: 2.25rem;
        height: 2.25rem;
        margin: 0.1rem;
        margin-top: 0.13rem;
        margin-left: 0.15rem;
      }
    }
  }
  &__input-field:disabled + .toggle-button__toogle-label {
    cursor: not-allowed;
  }

  &__label-content {
    font-size: 1.5rem;
    color: var(--color-text);
    font-weight: 600;
    cursor: pointer;
  }
  p {
    cursor: pointer;
  }
}
