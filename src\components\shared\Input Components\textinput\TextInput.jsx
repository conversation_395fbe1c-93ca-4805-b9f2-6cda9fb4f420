import React, { useState } from "react";
import "./TextInput.scss";
import ImportSvg from "../../importsvg/ImportSvg";

const TextInput = ({
  type = "text",
  label,
  id,
  placeholder,
  value,
  onChange,
  regex,
  className = "",
  disabled = false,
  infoType,
  infoMsg = "",
  variant = "medium",
  inputRef,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isAutofilled, setIsAutofilled] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  const handleAnimationStart = (e) => {
    if (e.animationName === "autofill") setIsAutofilled(true);
  };

  const handleChange = (e) => {
    const newValue = e.target.value;
    if (!regex || regex.test(newValue) || newValue === "") {
      onChange(e);
    }
    if (newValue === "") {
      setIsAutofilled(false);
    }
  };

  const isValuePresent =
    isFocused || value || isAutofilled || type === "date" || placeholder;
  const labelClass = `text-input__label text-input__label--${variant} ${
    isValuePresent ? `text-input__label--focused-${variant}` : ""
  }`;
  const inputClass = `text-input__input ${
    infoMsg ? `text-input__input--${infoType}` : ""
  } ${
    placeholder || isValuePresent
      ? `text-input__input--placeholder-available-${variant}`
      : ""
  } text-input__input--${variant}`;
  const containerClass = `text-input ${className} ${
    disabled ? "disabled" : ""
  }`;
  const infoClass = `text-input__info text-input__info--${variant} ${
    infoMsg ? `text-input__${infoType} ` : ""
  }`;

  return (
    <>
      <div className={containerClass}>
        <label className={labelClass} htmlFor={id}>
          {label}
        </label>
        <input
          type={type}
          id={id}
          value={value}
          placeholder={placeholder}
          className={inputClass}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={handleChange}
          onAnimationStart={handleAnimationStart}
          disabled={disabled}
          ref={inputRef}
        />
        <span className={infoClass}>
          <ImportSvg
            icon="info"
            className={`text-input__info-icon--${variant}`}
          />
          {infoMsg}
        </span>
      </div>
    </>
  );
};

export default TextInput;
