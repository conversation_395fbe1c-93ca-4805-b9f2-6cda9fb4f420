.text-input {
  position: relative;
  display: flex;
  flex: 1;
  border: 0.5px solid var(--color-stroke);
  border-radius: 2px;
  &__input {
    font-family: inherit;
    color: inherit;
    border-radius: 2px;
    background-color: transparent;
    border: none;
    display: block;
    transition: all 0.3s;
    border-bottom: 2px solid transparent;
    flex: 1;
    background-color: var(--color-extreme-2);
    padding: 0.8rem 1.2rem;
    &:focus {
      outline: none;
      box-shadow: var(--shadow-input);
      border-bottom: 2px solid var(--color-primary);
    }
    &--placeholder-available {
      &::-webkit-input-placeholder {
        color: var(--color-text-secondary);
        letter-spacing: 0.3px;
        font-size: 1.2rem;
      }
      &:not(:placeholder-shown, :focus) {
        background-color: var(--color-secondary);
      }
    }
    &--placeholder-available-large {
      &::-webkit-input-placeholder {
        color: var(--color-text-secondary);
        letter-spacing: 0.3px;
        font-size: 1.2rem;
      }
      &:not(:placeholder-shown, :focus) {
        background-color: var(--color-secondary);
      }
    }
    &--placeholder-available-medium {
      &::-webkit-input-placeholder {
        color: var(--color-text-secondary);
        letter-spacing: 0.3px;
        font-size: 1rem;
      }
      &:not(:placeholder-shown, :focus) {
        background-color: var(--color-secondary);
      }
    }
    &--large {
      font-size: 1.5rem;
      padding: 0.8rem 1.2rem;
    }
    &--medium {
      font-size: 1.2rem;
      padding: 0.5rem 1rem;
    }
    &--error {
      border: 0.5px solid var(--color-red-primary);
      background-color: var(--color-red-secondary);
      color: var(--color-red-primary);
      &:focus {
        outline: none;
        box-shadow: var(--shadow-input);
        border-bottom: 2px solid var(--color-red-primary);
      }
      &::-webkit-input-placeholder {
        color: var(--color-red-primary);
      }
      &:not(:placeholder-shown, :focus) {
        background-color: var(--color-red-secondary);
      }
    }
    &--warning {
      border: 0.5px solid var(--color-yellow-primary);
      background-color: var(--color-yellow-secondary);
      color: var(--color-yellow-primary);
      &:focus {
        outline: none;
        box-shadow: var(--shadow-input);
        border-bottom: 2px solid var(--color-yellow-primary);
      }
      &::-webkit-input-placeholder {
        color: var(--color-yellow-primary);
      }
      &:not(:placeholder-shown, :focus) {
        background-color: var(--color-yellow-secondary);
      }
    }
    &--success {
      border: 0.5px solid var(--color-green-primary);
      background-color: var(--color-green-secondary);
      color: var(--color-green-primary);
      &:focus {
        outline: none;
        box-shadow: var(--shadow-input);
        border-bottom: 2px solid var(--color-green-primary);
      }
      &::-webkit-input-placeholder {
        color: var(--color-green-primary);
      }
      &:not(:placeholder-shown, :focus) {
        background-color: var(--color-green-secondary);
      }
    }
  }

  &__label {
    font-size: 1.2rem;
    font-weight: 600;
    margin-left: 2rem;
    display: block;
    transition: all 0.3s;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    white-space: nowrap;
    &--focused {
      transform: translateX(-2rem) translateY(calc(-100% - 0.5rem));
      font-size: 1rem;
      position: absolute;
      top: 0;
    }
    &--large {
      font-size: 1.2rem;
    }
    &--medium {
      font-size: 1rem;
    }
    &--focused-large {
      transform: translateX(-2rem) translateY(calc(-100% - 0.5rem));
      font-size: 1rem;
      position: absolute;
      top: 0;
    }
    &--focused-medium {
      transform: translateX(-1rem) translateY(calc(-100% - 0.2rem));
      font-size: 0.9rem;
      position: absolute;
      top: 0;
      margin-left: 1rem;
    }
  }
  &.disabled {
    opacity: 0.8;
    .text-input__input--placeholder-available:not(:placeholder-shown, :focus) {
      background-color: unset;
    }
  }
  &__info {
    margin-top: 0.5rem;
    opacity: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s;
    position: absolute;
    bottom: 0;
    transform: translate(0, 100%);
    &--medium {
      font-size: 1rem;
      min-height: 1.5rem;
    }
    &--large {
      font-size: 1.2rem;
      min-height: 2rem;
    }
  }
  &__error {
    opacity: 1;
    color: var(--color-red-primary);
  }
  &__warning {
    opacity: 1;
    color: var(--color-yellow-primary);
  }
  &__success {
    opacity: 1;
    color: var(--color-green-primary);
  }
  &__info-icon {
    &--medium {
      width: 1.2rem;
      height: 1.2rem;
    }
    &--large {
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

@keyframes autofill {
  to {
    /* You can keep it empty or change properties */
  }
}

input:-webkit-autofill {
  animation-name: autofill;
  animation-duration: 0.1s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: both;
}
