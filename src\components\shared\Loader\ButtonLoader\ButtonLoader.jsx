import React from 'react';
import './ButtonLoader.scss';

function ButtonLoader({width='3rem', height='3rem', loading, loaderColor='white'}) {
  return (
    <div className={`button-loader ${!loading && 'button-loader--disable'}`}>
      <div className="button-loader__loader-circle" style={{width:width, height:height}}>
        <span style={{'--i':0,'--bg-color': loaderColor}}></span>
        <span style={{'--i':1,'--bg-color': loaderColor}}></span>
        <span style={{'--i':2,'--bg-color': loaderColor}}></span>
        <span style={{'--i':3,'--bg-color': loaderColor}}></span>
        <span style={{'--i':4,'--bg-color': loaderColor}}></span>
        <span style={{'--i':5,'--bg-color': loaderColor}}></span>
        <span style={{'--i':6,'--bg-color': loaderColor}}></span>
        <span style={{'--i':7,'--bg-color': loaderColor}}></span>
        <span style={{'--i':8,'--bg-color': loaderColor}}></span>
        <span style={{'--i':9,'--bg-color': loaderColor}}></span>
        <span style={{'--i':10,'--bg-color': loaderColor}}></span>
      </div>
    </div>
  )
}

export default ButtonLoader;
