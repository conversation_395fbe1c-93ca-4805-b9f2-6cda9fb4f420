.button-loader{
    display: flex;
    width: min-content;
    height: min-content;
    opacity: 1;
    &--disable{
        opacity: 0;
    }
    &__loader-circle{
        position: relative;
        span{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform: rotate(calc(36deg * var(--i)));
            &::before{
                content: '';
                position: absolute;
                right: 0;
                width: 20%;
                height: 20%;
                top: 40%;
                border-radius: 50%;
                background-color: var(--bg-color);
                // box-shadow: 0 0 10px var(--bg-color);
                animation: animate 1s linear infinite;
                animation-delay: calc(.1s * var(--i));
            }
        }
    }
}

@keyframes animate{
    0%{
        transform: scale(.9);
    }
    25%{
        transform: scale(.6);
    }
    50%{
        transform: scale(.3);
    }
    100%{
        transform: scale(0.2);
    }
}