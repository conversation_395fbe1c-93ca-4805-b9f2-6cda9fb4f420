.fullpage-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: var(--color-background);
  z-index: 1000;
  animation: zoomEffect 2s infinite ease-in-out;
  &.page-loader {
    display: flex;
    position: unset;
    width: unset;
    height: unset;
    overflow: hidden;
    animation: pageZoomEffect 2s infinite ease-in-out;
  }
  &__icon {
    width: 4rem;
    height: 4rem;
    transition: transform -0.4s ease-in-out;
  }
  &__text {
    font-weight: 700;
    font-size: 1.8rem;
    letter-spacing: 0.1px;
    text-transform: uppercase;
    transform: translateY(1rem);
    transition: transform 0.4s ease-in-out;
  }

  @keyframes zoomEffect {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
  }

  @keyframes pageZoomEffect {
    0%,
    100% {
      transform: scale(0.8);
    }
    50% {
      transform: scale(1);
    }
  }
}
