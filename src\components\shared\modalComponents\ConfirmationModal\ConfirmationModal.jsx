import React from "react";
import ModalWrapper from "../ModalWrapper/ModalWrapper";
import ButtonLoader from "../../Loader/ButtonLoader/ButtonLoader";

const ConfirmationModal = ({
  displayModal,
  closeModal,
  handleConfirmation,
  content,
  loading,
}) => {
  return (
    <ModalWrapper
      displayModal={displayModal}
      closeModal={loading ? () => null : closeModal}>
      <div className='modal__confirmation'>
        <h4 className='modal__title'>{content?.title}</h4>
        <p className='modal__message'>{content?.message}</p>
        <div className='modal__btn-container'>
          <button
            className='btn btn__secondary'
            onClick={loading ? () => null : closeModal}>
            No
          </button>
          <button
            className='btn btn__primary btn__progress'
            onClick={handleConfirmation}
            disabled={loading}>
            {!loading ? (
              "Yes"
            ) : (
              <ButtonLoader
                height='2rem'
                width='2rem'
                loaderColor='white'
                loading={loading}
              />
            )}
          </button>
        </div>
      </div>
    </ModalWrapper>
  );
};

export default ConfirmationModal;
