import React from "react";
import "./ModalWrapper.scss";
import useEsc<PERSON>ey from "../../../../utilities/hooks/useEscKey";

const ModalWrapper = ({
  displayModal = false,
  closeModal,
  zIndex = 10,
  children,
}) => {
  useEscKey(() => closeModal());
  return (
    <dialog
      className={`modal ${displayModal ? "" : "modal--hidden"}`}
      onClick={closeModal}
      style={{ zIndex }}
    >
      <div
        className={`modal__content ${
          displayModal ? "" : "modal__content--hidden"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </dialog>
  );
};

export default ModalWrapper;
