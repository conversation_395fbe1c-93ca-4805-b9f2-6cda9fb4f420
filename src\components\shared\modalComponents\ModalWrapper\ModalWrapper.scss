.modal {
  position: fixed;
  width: 100vw;
  height: 100vh;
  backdrop-filter: blur(5px);
  top: 0;
  left: 0;
  opacity: 1;
  display: flex;
  background-color: rgba(0, 0, 0, 0.3);
  justify-content: flex-end;
  transition: opacity 0.2s 0.1s;
  display: flex;
  justify-content: center;
  align-items: center;
  &--hidden {
    opacity: 0;
    pointer-events: none;
  }
  &__content {
    display: flex;
    background-color: var(--color-extreme-2);
    scale: 1;
    opacity: 1;
    border-radius: 1rem;
    transition: scale 0.2s var(--transition-animation-1) 0.1s, opacity 0.3s 0.1s;
    padding: 2rem 1rem;
    &--hidden {
      scale: 0.6;
      opacity: 0;
    }
  }
  &__confirmation {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: var(--color-text);
  }
  &__title {
    max-width: 35rem;
    font-size: 1.4rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &__message {
    color: var(--color-text-secondary);
    max-width: 35rem;
    text-align: center;
  }
  &__btn-container {
    display: flex;
    gap: 2rem;
    .btn {
      border-radius: 0.5rem;
      padding: 0.5rem 2.5rem;
    }
  }
}
