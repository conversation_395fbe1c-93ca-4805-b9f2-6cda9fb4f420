import React from "react";
import ImportSvg from "../../importsvg/ImportSvg";
import "./SlidingFormWrapper.scss";

const SlidingFormWrapper = ({
  displayForm = false,
  closeForm,
  zIndex = 10,
  children,
}) => {
  return (
    <dialog
      className={`sliding-form ${displayForm ? "" : "sliding-form--hidden"}`}
      onClick={() => closeForm()}
      style={{ zIndex }}
    >
      <div
        className={`sliding-form__form-container ${
          displayForm ? "" : "sliding-form__form-container--hidden"
        }`}
        onClick={(e) => e.stopPropagation()}
        style={{ zIndex: zIndex + 1 }}
      >
        {children}
        <button
          className="sliding-form__close-btn"
          onClick={() => closeForm()}
          style={{ zIndex: zIndex + 1 }}
        >
          <ImportSvg
            icon="right-arrow"
            className={`sliding-form__close-icon  ${
              displayForm ? "" : "sliding-form__close-icon--hidden"
            }`}
          />
        </button>
      </div>
    </dialog>
  );
};

export default SlidingFormWrapper;
