.sliding-form {
  position: fixed;
  width: 100vw;
  height: 100vh;
  backdrop-filter: blur(5px);
  top: 0;
  left: 0;
  z-index: 10;
  opacity: 1;
  display: flex;
  background-color: rgba(0, 0, 0, 0.3);
  justify-content: flex-end;
  transition: opacity 0.2s 0.1s;
  &--hidden {
    opacity: 0;
    pointer-events: none;
  }
  &__form-container {
    min-width: 50rem;
    background-color: var(--color-extreme-2);
    margin-left: 1rem;
    border-radius: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    position: relative;
    z-index: 11;
    transition: transform 0.3s ease-in-out;
    padding: 1rem 2.5rem;
    &--hidden {
      transform: translateX(100%);
    }
  }

  &__close-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: white;
    transition: transform 0.2s;
    &--hidden {
      transform: rotate(180deg);
    }
  }

  &__close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-primary);
    border-radius: 50%;
    cursor: pointer;
    padding: 0.25rem;
    position: absolute;
    top: 10%;
    left: 0;
    transform: translateX(-50%);
    z-index: 12;
  }
}
