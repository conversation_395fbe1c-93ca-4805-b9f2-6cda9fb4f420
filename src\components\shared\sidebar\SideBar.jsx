import React, { useEffect, useRef, useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import "./SideBar.scss";
import ImportMenuSvg from "./ImportmenuSvg";
import MainLogo from "../Components/mainlogo/MainLogo";
import ImportSvg from "../importsvg/ImportSvg";
import useClickOutside from "../../../utilities/hooks/useClickOutside";

const SideBar = () => {
  const location = useLocation();
  const [activeSubmenu, setActiveSubmenu] = useState({});
  const [activeSubIndex, setActiveSubIndex] = useState({});
  const [displayMenu, setDisplayMenu] = useState(true);
  const sidebarRef = useRef(null);
  const sidebarCloseBtnRef = useRef(null);
  useClickOutside(
    [sidebarRef, sidebarCloseBtnRef],
    () => setDisplayMenu(false),
    window.innerWidth < 768 && displayMenu
  );

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setDisplayMenu(false);
      }
    };

    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleSubmenu = (menuKey) => {
    setActiveSubmenu((prev) => ({
      ...prev,
      [menuKey]: !prev[menuKey],
    }));
  };

  const menuConfig = [
    {
      key: "dashboard",
      label: "dashboard",
      icon: "dashboard",
      path: "/dashboard",
      alterPath: "/",
    },
    {
      key: "users",
      label: "users",
      icon: "attendance",
      path: "/users",
      subMenu: [
        {
          label: "roles",
          path: "/users/roles",
          icon: "attendance",
        },
        {
          label: "Attendance",
          path: "/users/attendance",
          icon: "attendance",
        },
      ],
    },
    {
      key: "company",
      label: "Workspace",
      icon: "company",
      path: "/company",
    },
    {
      key: "leads",
      label: "leads",
      icon: "leads",
      path: "/leads",
      subMenu: [
        {
          label: "Contact",
          path: "/leads/lead-details",
          icon: "leads",
        },
        {
          label: "Deals",
          path: "/leads/deals",
          icon: "leads",
        },
      ],
    },
    { key: "settings", label: "settings", icon: "settings", path: "/settings" },
    { key: "comments", label: "comments", icon: "comments", path: "/comments" },
  ];

  useEffect(() => {
    menuConfig.forEach((menu) => {
      if (menu.subMenu) {
        const activeIndex = menu.subMenu.findIndex((subMenuItem) =>
          location.pathname.startsWith(subMenuItem.path)
        );
        if (activeIndex !== -1) {
          setActiveSubmenu((prev) => ({ ...prev, [menu.key]: true }));
          setActiveSubIndex((prev) => ({ ...prev, [menu.key]: activeIndex }));
        } else {
          setActiveSubIndex((prev) => ({
            ...prev,
            [menu.key]: -2,
          }));
        }
      }
    });
  }, [location.pathname]);

  return (
    <>
      <div
        className={`sidebar__overlay ${
          displayMenu ? "sidebar__overlay--active" : ""
        }`}
        onClick={(e) => {
          e.stopPropagation();
        }}
      />
      <aside
        className={`sidebar ${displayMenu ? "sidebar--active" : ""}`}
        ref={sidebarRef}
      >
        <div className="sidebar__logo">
          <MainLogo />
        </div>
        <nav className="sidebar__menu">
          <button
            className={`sidebar__close-btn ${
              displayMenu ? "sidebar__close-btn--active" : ""
            }`}
            onClick={() => setDisplayMenu((prev) => !prev)}
            ref={sidebarCloseBtnRef}
          >
            <ImportSvg
              icon="right-arrow"
              className={`sidebar__close-icon  ${
                displayMenu ? "" : "sidebar__close-icon--hidden"
              }`}
            />
          </button>
          {menuConfig.map((menu) => (
            <div key={menu.key} className="sidebar__menu-group">
              <NavLink
                to={menu.path}
                className={`sidebar__menu-item ${
                  location.pathname.startsWith(menu.path) ||
                  location.pathname === menu.alterPath
                    ? "sidebar__menu-item--active"
                    : ""
                }`}
              >
                <div className="sidebar__menu-title">
                  <ImportMenuSvg icon={menu.icon} className="sidebar__icon" />
                  <span>{menu.label}</span>
                  {menu.subMenu && (
                    <ImportMenuSvg
                      icon="arrow"
                      className={`sidebar__menu-arrow ${
                        activeSubmenu[menu.key]
                          ? "sidebar__menu-arrow--active"
                          : ""
                      }`}
                      onClick={(e) => {
                        e.preventDefault();
                        toggleSubmenu(menu.key);
                      }}
                    />
                  )}
                </div>
              </NavLink>
              {menu.subMenu && (
                <div
                  className={`sidebar__sub-menu ${
                    activeSubmenu[menu.key] ? "sidebar__sub-menu--active" : ""
                  }`}
                  style={{
                    "--active-transform": `translateY(calc(${
                      activeSubIndex[menu.key]
                    } * 100%))`,
                  }}
                >
                  <div
                    className="sidebar__sub-menu-container"
                    style={{
                      "--active-transform": `translateY(calc(${
                        activeSubIndex[menu.key]
                      } * 100%))`,
                    }}
                  >
                    {menu.subMenu.map((subMenuItem, index) => (
                      <NavLink
                        key={index}
                        to={subMenuItem.path}
                        className={`sidebar__menu-item sidebar__sub-menu-item ${
                          location.pathname.startsWith(subMenuItem.path)
                            ? "sidebar__sub-menu-item--active"
                            : ""
                        }`}
                      >
                        <div className="sidebar__menu-title">
                          <ImportMenuSvg
                            icon={subMenuItem.icon}
                            className="sidebar__icon"
                          />
                          <span>{subMenuItem.label}</span>
                        </div>
                      </NavLink>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </nav>
      </aside>
    </>
  );
};

export default SideBar;
