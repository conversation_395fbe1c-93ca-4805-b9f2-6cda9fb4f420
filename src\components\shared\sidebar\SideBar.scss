.sidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--color-extreme-2);
  gap: 2rem;
  position: relative;
  margin: 0.5rem;
  z-index: 5;
  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: var(--color-modal);
    backdrop-filter: blur(2px);
    z-index: 4;
    display: none;
    @media (max-width: 768px) {
      &--active {
        display: block;
      }
    }
  }
  &__close-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: white;
    transition: transform 0.2s;
    transform: rotateY(180deg);
    &--hidden {
      transform: rotateY(0);
    }
  }

  &__close-btn {
    display: none;
    @media (max-width: 768px) {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-primary);
      border-radius: 50%;
      cursor: pointer;
      padding: 0.25rem;
      position: absolute;
      top: 6rem;
      right: 0;
      transform: translateX(80%);
      z-index: 1;
      transition: transform 0.3s;
      &--active {
        transform: translateX(50%);
      }
    }
  }
  @media (max-width: 768px) {
    position: absolute;
    top: 0;
    left: 0;
    height: calc(100% - 1rem);
    z-index: 5;
    transform: translateX(calc(-100% - 0.5rem));
    transition: transform 0.3s;
  }
  &--active {
    @media (max-width: 768px) {
      transform: translateX(0);
    }
  }
  &__logo {
    padding: 1.5rem 2rem;
    border-bottom: 0.5px solid var(--color-stroke);
  }
  &__menu {
    display: flex;
    flex-direction: column;
  }
  &__menu-item {
    display: flex;
    gap: 0.5rem;
    color: var(--color-text-secondary);
    border-right: 0.4rem solid transparent;
    text-decoration: none;
    padding: 1rem;
    font-weight: 600;
    &:visited,
    &:not(:visited) {
      color: var(--color-text-secondary);
    }
    &--active,
    &:hover {
      background-color: var(--color-secondary);
      color: var(--color-text) !important;
    }
    &--active {
      border-right: 0.4rem solid var(--color-primary);
    }
  }
  &__icon {
    width: 1.7rem;
    height: 1.7rem;
  }
  &__menu-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    span {
      text-transform: capitalize;
    }
  }
  &__menu-arrow {
    width: 2.5rem;
    height: 2.5rem;
    position: absolute;
    right: 1rem;
    transition: transform 0.3s;
    &--active {
      transform: rotateX(180deg);
    }
  }
  &__sub-menu {
    display: grid;
    grid-template-rows: 0fr;
    overflow: hidden;
    transition: all 0.2s;
    position: relative;
    &--active {
      grid-template-rows: 1fr;
      padding: 1rem 0;
      &::after {
        transform: var(--active-transform, translateY(0));
      }
    }
    &::before {
      position: absolute;
      display: block;
      content: "";
      width: 0.2rem;
      background-color: var(--color-secondary);
      height: 100%;
      left: 1.8rem;
    }
    &::after {
      position: absolute;
      display: block;
      content: "";
      width: 0.2rem;
      height: 4rem;
      left: 1.8rem;
      background-color: var(--color-primary);
      margin-top: 1rem;
      transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    }
  }
  &__sub-menu-container {
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  &__sub-menu-item {
    margin-left: 2rem;
    padding-left: 0.5rem;
    padding-right: unset;
    &--active,
    &:hover {
      font-weight: 700;
      color: var(--color-text) !important;
      background-color: transparent;
    }
  }
}
