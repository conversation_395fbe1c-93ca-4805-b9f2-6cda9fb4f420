import React, { useEffect, useMemo, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

import ImportSvg from "../importsvg/ImportSvg";
import CustomToast from "../Components/CustomToast/CustomToast";
import ConfirmationModal from "../modalComponents/ConfirmationModal/ConfirmationModal";

import useApiHandler from "../../../utilities/hooks/useApiHandler";
import { useToast } from "../../../utilities/hooks/useToast";
import useClickOutside from "../../../utilities/hooks/useClickOutside";
import { logout } from "../../../store/actions/loginSignupAction";

import "./Topbar.scss";
import ImportMenuSvg from "../sidebar/ImportmenuSvg";

const ROUTE_TITLES = {
  dashboard: "Dashboard",
  roles: "Roles Management",
  users: "Users Management",
  company: "Workspace Management",
  leads: "Leads Management",
  settings: "Settings",
  comments: "Comments",
};

const Topbar = () => {
  const location = useLocation();
  const toast = useToast();
  const callApi = useApiHandler();
  const loading = useSelector((state) => state.loginSignup.logout.isLoading);

  // Refs for click‐outside detection
  const profileRef = useRef();
  const profileBtnRef = useRef();
  const themeRef = useRef();
  const themeBtnRef = useRef();

  // State
  const [currentComponent, setCurrentComponent] = useState("Dashboard");
  const [profileOpen, setProfileOpen] = useState(false);
  const [themeOpen, setThemeOpen] = useState(false);
  const [confirmLogout, setConfirmLogout] = useState(false);
  const [theme, setTheme] = useState(localStorage.getItem("theme") || "system");

  // Detects OS‐level preference (light vs dark)
  const prefersLight = window.matchMedia(
    "(prefers-color-scheme: light)"
  ).matches;

  // topbarData is a constant object we use everywhere
  const topbarData = useMemo(
    () => ({
      userName: localStorage.getItem("first_name") || "",
      userRole: "Super Admin",
      userProfile:
        "https://gratisography.com/wp-content/uploads/2024/10/gratisography-cool-cat-800x525.jpg",
    }),
    []
  );

  // Close profile & theme menus if clicking outside
  useClickOutside([profileRef, profileBtnRef], () => {
    setProfileOpen(false);
    setThemeOpen(false);
  });
  // Close theme menu if clicking outside only theme area
  useClickOutside([themeRef, themeBtnRef], () => {
    setThemeOpen(false);
  });

  // Update "currentComponent" whenever the URL changes
  useEffect(() => {
    const matchedKey = Object.keys(ROUTE_TITLES).find((key) =>
      location.pathname.includes(key)
    );
    setCurrentComponent(ROUTE_TITLES[matchedKey] || "Dashboard");
  }, [location.pathname]);

  // Persist theme choice and apply CSS attribute
  useEffect(() => {
    localStorage.setItem("theme", theme);

    if (theme === "dark") {
      document.documentElement.setAttribute("data-theme", "dark");
    } else if (theme === "light") {
      document.documentElement.removeAttribute("data-theme");
    } else {
      // "system" mode
      if (prefersLight) {
        document.documentElement.removeAttribute("data-theme");
      } else {
        document.documentElement.setAttribute("data-theme", "dark");
      }
    }
  }, [theme, prefersLight]);

  // Logout handler
  const handleLogout = () => {
    callApi(
      logout,
      { user_email: localStorage.getItem("user_email") },
      (response) => {
        toast(response?.message, "success");
        localStorage.removeItem("token");
        localStorage.removeItem("user_id");
        localStorage.removeItem("user_email");
        localStorage.removeItem("first_name");
        window.location.href = "/";
      },
      (error) => {
        toast("Logout Failed", error, "error");
      }
    );
  };

  // Renders one row inside the Theme submenu
  const ThemeOption = ({ label, value }) => {
    const isActive = theme === value;
    const isSystem = value === "system";
    const lightPreview = isSystem ? prefersLight : value === "light";

    return (
      <div
        className={`topbar__user-details-profile-theme-menu-item ${
          isActive ? "topbar__user-details-profile-theme-menu-item--active" : ""
        }`}
        onClick={(e) => {
          e.stopPropagation();
          setTheme(value);
        }}
      >
        {label}
        <div className="topbar__user-details-profile-theme-menu-preview-container">
          <div
            className={`topbar__user-details-profile-theme-menu-preview ${
              lightPreview
                ? "topbar__user-details-profile-theme-menu-preview--light"
                : "topbar__user-details-profile-theme-menu-preview--dark"
            }`}
          >
            <div
              className={`topbar__user-details-profile-theme-menu-preview-item ${
                lightPreview
                  ? "topbar__user-details-profile-theme-menu-preview-item--light"
                  : "topbar__user-details-profile-theme-menu-preview-item--dark"
              }`}
            />
            <div className="topbar__user-details-profile-theme-menu-preview-line-container">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className={`topbar__user-details-profile-theme-menu-preview-line ${
                    lightPreview
                      ? "topbar__user-details-profile-theme-menu-preview-line--light"
                      : "topbar__user-details-profile-theme-menu-preview-line--dark"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <header className="topbar">
        <h1 className="topbar__heading">
          <ImportMenuSvg icon="main-logo" className="topbar__logo" />
          {currentComponent}
        </h1>

        <div
          className={`topbar__user-details ${
            profileOpen ? "topbar__user-details--active" : ""
          }`}
        >
          {/* ===== PROFILE DROPDOWN CONTAINER ===== */}
          <div
            className={`topbar__user-details-profile shrink-container ${
              profileOpen
                ? "topbar__user-details-profile--open"
                : "shrink-container--shrink"
            }`}
            ref={profileRef}
          >
            {(profileOpen && (
              <div className="topbar__user-details-profile-menu">
                <div className="topbar__user-details-profile-menu-item">
                  Profile
                </div>
                <div className="topbar__user-details-profile-menu-item">
                  Settings
                </div>

                {/* ===== THEME SUBMENU TRIGGER ===== */}
                <div
                  className={`topbar__user-details-profile-menu-item ${
                    themeOpen
                      ? "topbar__user-details-profile-menu-item--active"
                      : ""
                  }`}
                  onClick={() => setThemeOpen((prev) => !prev)}
                  ref={themeBtnRef}
                >
                  Theme
                  <div className="topbar__user-details-profile-menu-item-icon">
                    <ImportSvg icon="down-arrow" />
                  </div>
                  {/* ===== THEME SUBMENU ITEMS ===== */}
                  <div
                    className={`topbar__user-details-profile-menu-item-submenu ${
                      themeOpen
                        ? "topbar__user-details-profile-menu-item-submenu--open"
                        : ""
                    }`}
                    ref={themeRef}
                  >
                    <div className="topbar__user-details-profile-theme-menu">
                      <ThemeOption label="Light" value="light" />
                      <ThemeOption label="Dark" value="dark" />
                      <ThemeOption label="System" value="system" />
                    </div>
                  </div>
                </div>

                <div
                  className="topbar__user-details-profile-menu-item"
                  onClick={() => {
                    setConfirmLogout(true);
                    setProfileOpen(false);
                  }}
                >
                  Logout
                </div>
              </div>
            )) || (
              <div className="topbar__user-details-profile-menu shrink-container__content" />
            )}
          </div>

          {/* ===== AVATAR + NAME ===== */}
          <CustomToast />
          <div className="topbar__user-details-container">
            {topbarData.userProfile ? (
              <img
                className="topbar__profile-img"
                src={topbarData.userProfile}
                alt="User"
              />
            ) : (
              <div className="topbar__profile-dummy">
                {topbarData.userName.charAt(0).toUpperCase()}
              </div>
            )}
            <div className="topbar__user-details-inner-cont">
              <p className="topbar__user-name">{topbarData.userName}</p>
              <p className="topbar__user-name topbar__user-role">
                {topbarData.userRole}
              </p>
            </div>
          </div>

          {/* ===== SETTINGS ICON (toggles profileOpen) ===== */}
          <ImportSvg
            icon="settings"
            className="topbar__svg"
            onClick={() => setProfileOpen((prev) => !prev)}
            ref={profileBtnRef}
          />
        </div>
      </header>

      {/* ===== LOGOUT CONFIRMATION MODAL ===== */}
      <ConfirmationModal
        displayModal={confirmLogout}
        closeModal={() => setConfirmLogout(false)}
        handleConfirmation={handleLogout}
        loading={loading}
        content={{
          title: "Logout Confirmation",
          message:
            "Are you sure you want to log out? Unsaved changes may be lost.",
        }}
      />
    </>
  );
};

export default Topbar;
