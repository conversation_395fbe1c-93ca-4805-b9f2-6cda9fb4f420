.topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0rem 2rem;
  padding: 1.34rem 0rem;
  border-bottom: 0.3px solid var(--color-stroke);
  &__logo {
    width: 4rem;
    height: 4rem;
    object-fit: contain;
    display: none;
    @media (max-width: 768px) {
      display: block;
    }
  }
  &__heading {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    color: var(--color-text);
    font-size: 2.5rem;
    font-weight: 600;
  }
  &__user-details {
    border: 1px solid var(--color-stroke);
    border-radius: 0.5rem;
    display: flex;
    width: max-content;
    padding: 0.5rem;
    align-items: center;
    gap: 3rem;
    justify-content: space-between;
    max-width: 25rem;
    position: relative;
    &--active {
      background-color: var(--color-extreme-2);
    }
  }
  &__user-details-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1 1;
    // overflow: hidden;
  }
  &__user-details-inner-cont {
    flex: 1 1;
    display: flex;
    flex-direction: column;
    // overflow: hidden;
  }
  &__profile-img {
    width: 3.5rem;
    height: 3.5rem;
    object-fit: cover;
    object-position: center;
    border-radius: 0.5rem;
  }
  &__profile-dummy {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 0.5rem;
    color: var(--color-green-primary);
    background-color: var(--color-green-secondary);
    align-items: center;
    display: flex;
    justify-content: center;
    font-weight: 700;
    font-size: 2rem;
  }
  &__svg {
    width: 2rem;
    height: 2rem;
    color: var(--color-primary);
    cursor: pointer;
  }
  &__user-name {
    color: var(--color-text);
    font-size: 1.2rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 600;
  }
  &__user-role {
    color: var(--color-text-secondary);
    font-weight: 500;
  }
  &__user-details-profile {
    position: absolute;
    right: -1px;
    top: 92%;
    width: calc(100% + 2px);
    background-color: var(--color-extreme-2);
    border-radius: 0 0 0.5rem 0.5rem;
    display: flex;
    flex-direction: column;
    z-index: 10;
    &--open {
      padding: 1rem;
      border: 1px solid var(--color-stroke);
      border-top: none;
      box-shadow: var(--shadow-primary-card);
      overflow: unset !important;
    }
  }
  &__user-details-profile-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  &__user-details-profile-menu-item {
    color: var(--color-text);
    font-size: 1.2rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    &:hover,
    &--active {
      background-color: var(--color-secondary);
    }
  }
  &__user-details-profile-menu-item-icon {
    width: 2rem;
    height: 2rem;
    color: var(--color-primary);
    overflow: hidden;
    transform: rotate(-90deg);
  }
  &__user-details-profile-menu-item-submenu {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    background-color: var(--color-extreme-2);
    border-radius: 0.5rem;
    display: flex;
    flex-direction: column;
    z-index: 10;
    padding: 1rem;
    box-shadow: var(--shadow-primary-card);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    &--open {
      transform: translateX(calc(-100% - 1.5rem));
      opacity: 1;
      pointer-events: all;
    }
  }
  &__user-details-profile-theme-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  &__user-details-profile-theme-menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    &:hover,
    &--active {
      background-color: var(--color-secondary);
    }
  }
  &__user-details-profile-theme-menu-preview-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1rem;
  }
  &__user-details-profile-theme-menu-preview {
    width: 6rem;
    height: 4rem;
    border-radius: 0.5rem;
    backdrop-filter: blur(5px);
    margin-left: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    &--light {
      background-color: var(--color-theme-reference-3);
    }
    &--dark {
      background-color: var(--color-theme-reference-4);
    }
  }
  &__user-details-profile-theme-menu-preview-item {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    &--light {
      background-color: var(--color-theme-reference-1);
    }
    &--dark {
      background-color: var(--color-theme-reference-2);
    }
    &--active {
      transform: scale(1.2);
    }
  }
  &__user-details-profile-theme-menu-preview-line-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
    flex: 1;
    width: 100%;
    justify-content: space-between;
  }
  &__user-details-profile-theme-menu-preview-line {
    width: 90%;
    height: 2px;
    border-radius: 0.5rem;
    &--light {
      background-color: var(--color-theme-reference-1);
    }
    &--dark {
      background-color: var(--color-theme-reference-2);
    }
  }
}
