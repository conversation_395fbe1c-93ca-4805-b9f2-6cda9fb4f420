import React from "react";
import "../components/loginSignupComponents/LoginSignUp.scss";
import { Outlet } from "react-router-dom";
import DynamicImageSequence from "../components/loginSignupComponents/DynamicImageSequence";
import img1 from "../assets/images/login-img-1.png";
import img2 from "../assets/images/login-img-2.png";
import img3 from "../assets/images/login-img-3.png";
import CustomToast from "../components/shared/Components/CustomToast/CustomToast";

const LoginPage = () => {
  const slides = [
    {
      image: img1,
      msg: "Swipe in and out with GPS accuracy for effortless attendance tracking.",
      quote: "Job Orders on Fleek",
      className: "img1",
    },
    {
      image: img2,
      msg: "Manage your tasks efficiently with real-time updates.",
      quote: "Productivity Simplified",
      className: "img2",
    },
    {
      image: img3,
      msg: "Stay connected with your team wherever you are.",
      quote: "Collaboration Made Easy",
      className: "img3",
    },
  ];
  return (
    <>
      <main className='container login-container'>
        <section className='container__toast'>
          <CustomToast />
        </section>
        <section className='container__left'>
          <Outlet />
        </section>
        <section className='container__right sequence__container'>
          <DynamicImageSequence
            slides={slides}
            interval={5000}
          />
        </section>
      </main>
    </>
  );
};

export default LoginPage;
