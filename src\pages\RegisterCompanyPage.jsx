import React from "react";
import "../components/registerCompanyComponents/RegisterCompany.scss";
import MainLogo from "../components/shared/Components/mainlogo/MainLogo";
import { Outlet } from "react-router-dom";
import CustomToast from "../components/shared/Components/CustomToast/CustomToast";
import CompanyProfilePreview from "../components/registerCompanyComponents/CompanyProfilePreview/CompanyProfilePreview";

const RegisterCompanyPage = () => {
  return (
    <main className='register-company'>
      <header className='register-company__header'>
        <MainLogo />
        <div className='register-company__header-topic'>
          <h2>Quick Setup</h2>
          <div className='register-company__toast-container'>
            <CustomToast />
          </div>
        </div>
      </header>
      <section className='register-company__form-preview'>
        <div className='register-company__form'>
          <Outlet />
        </div>
        <div className='register-company__preview'>
          <CompanyProfilePreview />
        </div>
      </section>
    </main>
  );
};

export default RegisterCompanyPage;
