import React from "react";
import SlidingFormWrapper from "../components/shared/modalComponents/SlidingFormWrapper/SlidingFormWrapper";
import FormConfiguration from "../components/formConfiguration/FormConfiguration";

const SettingsPage = () => {
  const [isFormConfig, setIsFormConfig] = React.useState(false);
  return (
    <div style={{ padding: "5rem" }}>
      <button
        className="btn btn__primary btn__elevated"
        onClick={() => setIsFormConfig(true)}
      >
        Form Configuration
      </button>
      <SlidingFormWrapper
        displayForm={isFormConfig}
        closeForm={() => setIsFormConfig(false)}
      >
        <FormConfiguration />
      </SlidingFormWrapper>
    </div>
  );
};

export default SettingsPage;
