import React, { lazy, Suspense } from "react";
import FullPageLoader from "../components/shared/Loader/FullPageLoader/FullPageLoader";

const LoginRoutes = lazy(() => import("./LoginRoutes"));
const CrmRoutes = lazy(() => import("./CrmRoutes"));

const AppRouter = () => {
  const isAuthenticated =
    !!localStorage.getItem("token") || !!sessionStorage.getItem("token");
  const [theme, setTheme] = React.useState(
    localStorage.getItem("theme") || "system"
  );

  // Detects OS‐level preference (light vs dark)
  const prefersLight = window.matchMedia(
    "(prefers-color-scheme: light)"
  ).matches;
  React.useLayoutEffect(() => {
    localStorage.setItem("theme", theme);

    if (theme === "dark") {
      document.documentElement.setAttribute("data-theme", "dark");
    } else if (theme === "light") {
      document.documentElement.removeAttribute("data-theme");
    } else {
      // "system" mode
      if (prefersLight) {
        document.documentElement.removeAttribute("data-theme");
      } else {
        document.documentElement.setAttribute("data-theme", "dark");
      }
    }
  }, [theme, prefersLight]);

  return (
    <Suspense fallback={<FullPageLoader />}>
      {isAuthenticated ? <CrmRoutes /> : <LoginRoutes />}
    </Suspense>
  );
};

export default AppRouter;
