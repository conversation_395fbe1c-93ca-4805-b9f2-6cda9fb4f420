import React, { lazy, Suspense } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { createTheme, ThemeProvider } from "@mui/material";
import { theme1, theme2 } from "../utilities/constants/muiTheme";
import FullPageLoader from "../components/shared/Loader/FullPageLoader/FullPageLoader";

// Lazy-loaded pages and components
const CompanyPage = lazy(() => import("../pages/CompanyPage"));
const DashboardPage = lazy(() => import("../pages/DashboardPage"));
const AttendancePage = lazy(() => import("../pages/AttendancePage"));
const LeadsPage = lazy(() => import("../pages/LeadsPage"));
const Leads = lazy(() => import("../components/LeadsComponents/Leads"));
const LeadPage = lazy(() =>
  import("../components/LeadsComponents/LeadPage/LeadPage")
);
const Deals = lazy(() => import("../components/dealsComponents/Deals"));
const SettingsPage = lazy(() => import("../pages/SettingsPage"));
const CommentsPage = lazy(() => import("../pages/CommentsPage"));
const UsersPage = lazy(() => import("../pages/UsersPage"));
const RolesPage = lazy(() => import("../pages/RolesPage"));
const RolesList = lazy(() => import("../components/rolesComponents/RolesList"));
const AddNewRole = lazy(() =>
  import("../components/rolesComponents/AddNewRole")
);
const Company = lazy(() => import("../components/companyComponents/Company"));
const CompanyProfile = lazy(() =>
  import("../components/companyComponents/CompanyProfile/CompanyProfile")
);
const AddCompany = lazy(() =>
  import("../components/companyComponents/AddCompany/AddCompany")
);

// Eagerly loaded shared layout
import Topbar from "../components/shared/topbar/Topbar";
import SideBar from "../components/shared/sidebar/SideBar";

const CrmRoutes = () => {
  const lightTheme = createTheme(theme1);
  const darkTheme = createTheme(theme2);
  const [selectedTheme, setSelectedTheme] = React.useState("light");
  React.useLayoutEffect(() => {
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-theme"
        ) {
          const newTheme =
            document.documentElement.getAttribute("data-theme") || "light";
          console.log("new theme", newTheme);
          setSelectedTheme(newTheme);
        }
      }
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["data-theme"],
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <ThemeProvider theme={selectedTheme === "dark" ? darkTheme : lightTheme}>
      <main className="container">
        <SideBar />
        <div className="container__content">
          <Topbar />
          <main className="container__main">
            <Suspense fallback={<FullPageLoader isPageLoader />}>
              <Routes>
                <Route path="/" element={<DashboardPage />} />
                <Route path="/dashboard" element={<DashboardPage />} />
                <Route path="/users" element={<UsersPage />} />
                <Route path="/users/roles" element={<RolesPage />}>
                  <Route index element={<RolesList />} />
                  <Route path="add-new-role" element={<AddNewRole />} />
                  <Route path="edit-role/" element={<AddNewRole />} />
                </Route>
                <Route path="/company" element={<CompanyPage />}>
                  <Route index element={<Company />} />
                  <Route path="add-company" element={<AddCompany />} />
                  <Route
                    path="profile/:companyId"
                    element={<CompanyProfile />}
                  />
                </Route>
                <Route path="/leads" element={<LeadsPage />}>
                  <Route index element={<Leads />} />
                  <Route path="lead-details" element={<LeadPage />} />
                  <Route path="deals" element={<Deals />} />
                </Route>
                <Route path="/settings" element={<SettingsPage />} />
                <Route path="/comments" element={<CommentsPage />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
          </main>
        </div>
      </main>
    </ThemeProvider>
  );
};

export default CrmRoutes;
