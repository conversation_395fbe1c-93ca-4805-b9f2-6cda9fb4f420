import React, { lazy, Suspense } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import FullPageLoader from "../components/shared/Loader/FullPageLoader/FullPageLoader";

// Lazy-loaded components
const LoginPage = lazy(() => import("../pages/LoginPage"));
const Login = lazy(() => import("../components/loginSignupComponents/Login"));
const ForgotPassword = lazy(() =>
  import("../components/loginSignupComponents/ForgotPassword")
);
const ResetPassword = lazy(() =>
  import("../components/loginSignupComponents/ResetPassword")
);
const OrganizationRegistration = lazy(() =>
  import("../components/loginSignupComponents/OrganizationRegistration")
);
const SignUp = lazy(() => import("../components/loginSignupComponents/SignUp"));

const RegisterCompanyPage = lazy(() => import("../pages/RegisterCompanyPage"));
const CompanyInformationForm = lazy(() =>
  import("../components/registerCompanyComponents/CompanyInformationForm")
);
const CompanyAdditionalInfoForm = lazy(() =>
  import("../components/registerCompanyComponents/CompanyAdditionalInfoForm")
);
const CompanyAddressForm = lazy(() =>
  import("../components/registerCompanyComponents/CompanyAddressForm")
);
const CompanySocialMediaForm = lazy(() =>
  import("../components/registerCompanyComponents/CompanySocialMediaForm")
);

const LoginRoutes = () => {
  return (
    <Suspense fallback={<FullPageLoader />}>
      <Routes>
        <Route path="/" element={<LoginPage />}>
          <Route index element={<Login />} />
          <Route path="sign-up" element={<SignUp />} />
          <Route path="forgot-password/" element={<ForgotPassword />} />
          <Route path="reset-password" element={<ResetPassword />} />
        </Route>

        <Route path="/account" element={<LoginPage />}>
          <Route index element={<OrganizationRegistration />} />
          <Route path="set-password" element={<ResetPassword />} />
          <Route path="signup" element={<SignUp />} />
          <Route path="user-set-password" element={<ResetPassword />} />
        </Route>

        <Route path="/register-company" element={<RegisterCompanyPage />}>
          <Route index element={<CompanyInformationForm />} />
          <Route path="address" element={<CompanyAddressForm />} />
          <Route
            path="additional-info"
            element={<CompanyAdditionalInfoForm />}
          />
          <Route path="social-media" element={<CompanySocialMediaForm />} />
        </Route>

        <Route path="/register-company/set-password" element={<LoginPage />}>
          <Route index element={<ResetPassword />} />
        </Route>

        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
};

export default LoginRoutes;
