import { createAsyncAction } from "../../utilities/createAction";

export const companyRegister = createAsyncAction(
  "companyRegister",
  "PUT",
  "/login_logout/company_register/",
  "multipart/form-data"
);

export const getModules = createAsyncAction(
  "getModule",
  "GET",
  "/modules/get_modules/"
);

export const getCompanyDetails = createAsyncAction(
  "getCompanyDetails",
  "POST",
  "/company/get_company_details"
);

export const getOrderedCompanyDetails = createAsyncAction(
  "getOrderedCompanyDetails",
  "POST",
  "/company/get_ordered_company_details"
);
