import { createAsyncAction } from "../../utilities/createAction";

export const loginView = createAsyncAction(
  "loginView",
  "POST",
  "/login_logout/login_view/"
);

export const organizationRegister = createAsyncAction(
  "organizationRegister",
  "POST",
  "/login_logout/organization_register/"
);


export const emailValidate = createAsyncAction(
  "emailValidate",
  "POST",
  "/login_logout/email_validate/"
);


export const forgetPassword = createAsyncAction(
  "forgetPassword",
  "POST",
  "/login_logout/forget_password/"
);

export const updatePassword = createAsyncAction(
  "updatePassword",
  "POST",
  "/login_logout/update_password/"
);

export const verifyUUID = createAsyncAction(
  "verifyUUID",
  "POST",
  "/login_logout/verify_UUID/"
);


export const logout = createAsyncAction(
  "logout",
  "POST",
  "/login_logout/logout/"
);
