import { createAsyncAction } from "../../utilities/createAction";

export const moduleFieldAccess = createAsyncAction(
  "moduleFieldAccess",
  "GET",
  "/company/module_field_access/USR_00000002/"
);

export const createRoles = createAsyncAction(
  "createRoles",
  "POST",
  "/roles/create_roles/"
);

export const getRoles = createAsyncAction(
  "getRoles",
  "GET",
  "/roles/get_role/USR_00000002/"
);

export const editRole = createAsyncAction(
  "editRole",
  "PUT",
  "/roles/edit_role/"
);
