import { createAsyncAction } from "../../utilities/createAction";

export const loginView = createAsyncAction(
  "loginView",
  "POST",
  "/login_logout/login_view/"
);

export const organizationRegister = createAsyncAction(
  "organizationRegister",
  "POST",
  "/login_logout/organization_register/"
);

export const emailValidate = createAsyncAction(
  "emailValidate",
  "POST",
  "/login_logout/email_validate/"
);


export const logout = createAsyncAction(
  "logout",
  "POST",
  "/login_logout/logout/"
);

export const updateCompanyDetails = createAsyncAction(
  "updateCompanyDetails",
  "PUT",
  "/company/update_company_details",
  "multipart/form-data"
);


