import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import {
  companyRegister,
  getCompanyDetails,
  getModules,
  getOrderedCompanyDetails,
} from "../actions/companyAction";

const companyRegisterSlice = createAsyncSlice(
  "companyRegisterSlice",
  companyRegister
);
const getModulesSlice = createAsyncSlice("getModulesSlice", getModules);
const getCompanyDetailsSlice = createAsyncSlice(
  "getCompanyDetailsSlice",
  getCompanyDetails
);

const getOrderedCompanyDetailsSlice = createAsyncSlice(
  "getOrderedCompanyDetailsSlice",
  getOrderedCompanyDetails
);

const companyReducer = combineReducers({
  companyRegister: companyRegisterSlice.reducer,
  getModules: getModulesSlice.reducer,
  getCompanyDetails: getCompanyDetailsSlice.reducer,
  getOrderedCompanyDetails: getOrderedCompanyDetailsSlice.reducer,
});

export default companyReducer;
