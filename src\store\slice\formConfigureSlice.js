import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import {
  createReferenceFields,
  referenceFields,
} from "../actions/fromConfigureAction";

const referenceFieldsSlice = createAsyncSlice(
  "referenceFieldsSlice",
  referenceFields
);

const createReferenceFieldsSlice = createAsyncSlice(
  "createReferenceFieldsSlice",
  createReferenceFields
);

const formConfigureReducer = combineReducers({
  referenceFields: referenceFieldsSlice.reducer,
  createReferenceFields: createReferenceFieldsSlice.reducer,
});

export default formConfigureReducer;
