import { combineReducers, createSlice } from "@reduxjs/toolkit";

const toastSlice = createSlice({
  name: "toasts",
  initialState: [],
  reducers: {
    addToast: (state, action) => {
      state.push(action.payload); // Add new toast
    },
    removeToast: (state, action) => {
      return state.filter((toast) => toast.id !== action.payload); // Remove toast by ID
    },
  },
});

const formDataSlice = createSlice({
  name: "formData",
  initialState: {},
  reducers: {
    setStoreFormData: (state, action) => {
      return { ...state, ...action.payload }; // Update or set form data
    },
    clearStoreFormData: () => {
      return {}; // Reset form data
    },
  },
});

const activeComponentSlice = createSlice({
  name: "activeComponent",
  initialState: {},
  reducers: {
    setActiveComponent: (state, action) => (state = action.payload),
  },
});

export const { addToast, removeToast } = toastSlice.actions;
export const { setStoreFormData, clearStoreFormData } = formDataSlice.actions;
export const { setActiveComponent } = activeComponentSlice.actions;

const generalReducer = combineReducers({
  toast: toastSlice.reducer,
  formData: formDataSlice.reducer,
  activeComponent: activeComponentSlice.reducer,
});

export default generalReducer;
