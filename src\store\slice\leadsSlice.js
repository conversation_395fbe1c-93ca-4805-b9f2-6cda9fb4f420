import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import { leadCreation, leadsList } from "../actions/leadsAction";

const leadCreationSlice = createAsyncSlice("userRegisterSlice", leadCreation);
const leadsListSlice = createAsyncSlice("leadsListSlice", leadsList);

const leadsReducer = combineReducers({
  leadCreation: leadCreationSlice.reducer,
  leadsList: leadsListSlice.reducer,
});

export default leadsReducer;
