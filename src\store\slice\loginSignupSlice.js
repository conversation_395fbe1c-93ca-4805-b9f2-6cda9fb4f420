import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import {
  forgetPassword,
  loginView,
  updatePassword,
  verifyUUID,
  emailValidate,
  logout,
} from "../actions/loginSignupAction";



const loginViewSlice = createAsyncSlice("loginViewSlice", loginView);
const organizationRegisterSlice = createAsyncSlice(
  "organizationRegister",
  loginView
);
const emailValidateSlice = createAsyncSlice("emailValidatelice", emailValidate);
const forgetPasswordSlice = createAsyncSlice(
  "forgetPasswordSlice",
  forgetPassword
);
const updatePasswordSlice = createAsyncSlice(
  "updatePasswordSlice",
  updatePassword
);
const verifyUUIDSlice = createAsyncSlice("verifyUUIDSlice", verifyUUID);
const logoutSlice = createAsyncSlice("logoutSlice",logout);

const adminReducer = combineReducers({
  loginView: loginViewSlice.reducer,
  organizationRegister: organizationRegisterSlice.reducer,
  emailValidate: emailValidateSlice.reducer,
  forgetPassword: forgetPasswordSlice.reducer,
  updatePassword: updatePasswordSlice.reducer,
  verifyUUID: verifyUUIDSlice.reducer,
  logout:logoutSlice.reducer
});

export default adminReducer;
