import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import {
  createRoles,
  editRole,
  getRoles,
  moduleFieldAccess,
} from "../actions/rolesAction";

const moduleFieldAccessSlice = createAsyncSlice(
  "moduleFieldAccessSlice",
  moduleFieldAccess
);

const createRolesSlice = createAsyncSlice("createRolesSlice", createRoles);

const getRolesSlice = createAsyncSlice("getRolesSlice", getRoles);

const editRoleSlice = createAsyncSlice("editRolesSlice", editRole);

const rolesReducer = combineReducers({
  moduleFieldAccess: moduleFieldAccessSlice.reducer,
  createRoles: createRolesSlice.reducer,
  getRoles: getRolesSlice.reducer,
  editRole: editRoleSlice.reducer,
});

export default rolesReducer;
