import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import { emailValidate, loginView, logout } from "../actions/loginSignupAction";
import { updateCompanyDetails } from "../actions/tempAction1";




const loginViewSlice = createAsyncSlice("loginViewSlice", loginView);
const organizationRegisterSlice = createAsyncSlice(
  "organizationRegister",
  loginView
);
const emailValidateSlice = createAsyncSlice("emailValidatelice", emailValidate);
const logoutSlice = createAsyncSlice("logoutSlice",logout);
const updateCompanyDetailsSlice = createAsyncSlice("updateCompanyDetailsSlice",updateCompanyDetails);

const temp1 = combineReducers({
  loginView: loginViewSlice.reducer,
  organizationRegister: organizationRegisterSlice.reducer,
  emailValidate: emailValidateSlice.reducer,
  logout:logoutSlice.reducer,
  updateCompanyDetails:updateCompanyDetailsSlice.reducer
});

export default temp1;
