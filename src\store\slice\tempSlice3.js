import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import { updatePassword, verifyUUID } from "../actions/tempAction3";

const updatePasswordSlice = createAsyncSlice("updatePasswordSlice", updatePassword);
const verifyUUIDSlice = createAsyncSlice("verifyUUIDSlice", verifyUUID);

const temp1 = combineReducers({
  updatePassword: updatePasswordSlice.reducer,
  verifyUUID: verifyUUIDSlice.reducer,
});

export default temp1;