import { combineReducers } from "@reduxjs/toolkit";
import createAsyncSlice from "../../utilities/createSlice";
import { allCompanyUsers, userRegister } from "../actions/usersAction";

const userRegisterSlice = createAsyncSlice("userRegisterSlice", userRegister);
const allCompanyUsersSlice = createAsyncSlice(
  "allCompanyUsersSlice",
  allCompanyUsers
);

const userReducer = combineReducers({
  userRegister: userRegisterSlice.reducer,
  allCompanyUsers: allCompanyUsersSlice.reducer,
});

export default userReducer;
