import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { Provider } from "react-redux";
import loginSignupReducer from "./slice/loginSignupSlice";
import tempSlice1Reducer from "./slice/tempSlice1";
import tempSlice2Reducer from "./slice/tempSlice2";
import tempSlice3Reducer from "./slice/tempSlice3";
import companyReducer from "./slice/companySlice";
import generalReducer from "./slice/generalSlice";
import formConfigureReducer from "./slice/formConfigureSlice";
import rolesReducer from "./slice/rolesSlice";
import userReducer from "./slice/usersSlice";
import leadsReducer from "./slice/leadsSlice";

const StoreProvider = ({ children }) => {
  const rootReducer = combineReducers({
    loginSignup: loginSignupReducer,
    company: companyReducer,
    formConfigure: formConfigureReducer,
    roles: rolesReducer,
    user: userReducer,
    leads: leadsReducer,
    tempSlice1: tempSlice1Reducer,
    tempSlice2: tempSlice2Reducer,
    tempSlice3: tempSlice3Reducer,
    general: generalReducer,
  });

  const store = configureStore({
    reducer: rootReducer,
  });

  return <Provider store={store}>{children}</Provider>;
};

export default StoreProvider;
