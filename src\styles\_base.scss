/*
COLORS

Primary: #315edd
Primary light: #f4f8ff
Primary dark: #475366

Grey light 1: #faf9f9
Grey light 2: #f4f2f2
Grey light 3: #f0eeee
Grey light 4: #ccc

Grey dark 1: #333
Grey dark 2: #777
Grey dark 3: #999

*/

:root {
  --color-primary: #502799;
  --color-primary-darker: #6651c7;
  --color-primary-hover: #39147a;
  --color-secondary: #ede8ff;
  --color-secondary-darker: #ebe0ff;
  --color-background: #f2eff8;
  --color-white: #fff;
  --color-extreme-1: #000;
  --color-extreme-2: #fff;
  --color-text: #2a2d33;
  --color-text-secondary: #7d7d7d;
  --color-extreme-2-hover: #f2f2f2;
  --color-green-primary: #28a745;
  --color-green-secondary: #e9ffe8;
  --color-yellow-primary: #ffc107;
  --color-yellow-secondary: #fffce5;
  --color-red-primary: #dc3545;
  --color-red-secondary: #ffe5e5;
  --color-blue-primary: #4a90e2;
  --color-blue-secondary: #e2efff;
  --color-modal: rgba(0, 0, 0, 0.3);
  --color-stroke: #e5e5e5;
  --color-stroke-hover: #d7d7d7;
  --color-gradient-background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  --color-theme-reference-1: #502799;
  --color-theme-reference-2: #823cff;
  --color-theme-reference-3: rgba(0, 0, 0, 0.08);
  --color-theme-reference-4: rgba(0, 0, 0, 0.8);
  --shadow: 0.2rem 0.4rem 0.8rem 0 rgba(0, 0, 0, 0.08);
  --shadow-input: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
  --shadow-input-active: 0 0.3rem 2rem rgba(0, 0, 0, 0.3);
  --shadow-primary: 0 1rem 2rem 0 var(--color-secondary-darker);
  --shadow-primary-card: 0px 4px 8px var(--color-secondary-darker),
    0px 1px 3px var(--color-secondary-darker);
  --transition-animation-1: cubic-bezier(0, -0.21, 1, 1.4);
}

[data-theme="dark"] {
  --color-primary: #823cff;
  --color-primary-darker: #9472ff;
  --color-primary-hover: #6721e2;
  --color-secondary: #382a49;
  --color-secondary-darker: #2f223b;
  --color-background: #1e1e1e;
  --color-extreme-1: #fff;
  --color-extreme-2: #000;
  --color-text: #ffffff;
  --color-text-secondary: #83899f;
  --color-extreme-2-hover: #1a1a1a;
  --color-green-primary: #37d67a;
  --color-green-secondary: #004407;
  --color-yellow-primary: #ffc94b;
  --color-yellow-secondary: #4c4300;
  --color-red-primary: #ff6f6f;
  --color-red-secondary: #5f0000;
  --color-blue-primary: #105db7;
  --color-blue-secondary: #213954;
  --color-stroke: #3a3a3a;
  --color-stroke-hover: #4a4a4a;
  --color-gradient-background: linear-gradient(
    180deg,
    #ffffff4d 0%,
    #ffffff00 100%
  );
  --color-theme-reference-3: rgba(255, 255, 255, 0.9);
  --color-theme-reference-4: rgba(255, 255, 255, 0.2);
  --shadow: 0.2rem 0.4rem 0.8rem 0 rgba(255, 255, 255, 0.08);
  --shadow-input: 0 0.5rem 2rem rgba(255, 255, 255, 0.25);
  --shadow-input-active: 0 0.3rem 2rem rgba(255, 255, 255, 0.2);
  --shadow-primary: 0 1rem 2rem 0 var(--color-secondary-darker);
  --shadow-primary-card: 0px 4px 8px var(--color-secondary-darker),
    0px 1px 3px var(--color-secondary-darker);
}

[data-theme="color-1-dark"] {
  --color-extreme-1: #fff;
  --color-extreme-2: #000;
  --color-extreme-2-hover: #222222;
  --color-modal: rgba(255, 255, 255, 0.1);
  --shadow: 0.2rem 0.4rem 0.8rem 0 rgba(255, 255, 255, 0.08);
}

[data-theme="color-2-dark"] {
  --color-extreme-1: #fff;
  --color-extreme-2: #000;
  --color-extreme-2-hover: #222222;
  --color-modal: rgba(255, 255, 255, 0.1);
  --shadow: 0.2rem 0.4rem 0.8rem 0 rgba(255, 255, 255, 0.08);
}

[data-theme="color-1-light"] {
  --color-extreme-1: #000;
  --color-extreme-2: #fff;
  --color-extreme-2-hover: #f2f2f2;
  --color-modal: rgba(0, 0, 0, 0.6);
  --shadow: 0.2rem 0.4rem 0.8rem 0 rgba(0, 0, 0, 0.08);
}

[data-theme="color-2-light"] {
  --color-extreme-1: #000;
  --color-extreme-2: #fff;
  --color-extreme-2-hover: #f2f2f2;
  --color-modal: rgba(0, 0, 0, 0.6);
  --shadow: 0.2rem 0.4rem 0.8rem 0 rgba(0, 0, 0, 0.08);
}

* {
  margin: 0;
  padding: 0;
  &:focus-visible {
    outline: 1px solid var(--color-primary);
  }
}

*,
*::after,
*::before {
  box-sizing: inherit;
  font-size: 1.2rem;
  transition: background-color 0.3s;
}

html {
  box-sizing: border-box;
  font-size: 62.5%; //10px
  @media (max-width: 1200px) {
    font-size: 56.25%; //9px
  }
  @media (max-width: 768px) {
    font-size: 50%; //8px
  }
  @media (max-width: 576px) {
    font-size: 43.75%; //7px
  }
  @media (min-width: 1800px) {
    font-size: 75%; //12px
  }
}

body {
  font-family: "poppins";
  font-weight: 400;
  color: var(--color-text);
}

button {
  border: none;
  background: none;
  cursor: pointer;
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
}

input,
button {
  font-family: inherit;
}

dialog {
  border: none;
}

ul,
ol,
li {
  list-style: none;
}

::-webkit-scrollbar {
  width: 0.3rem;
  height: 0.3rem;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 0.5rem;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-button-primary-1);
}

::-webkit-scrollbar-track {
  background-color: var(--color-secondary);
}
