.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.5rem;
  &__primary {
    background-color: var(--color-primary);
    color: var(--color-white);
    border: 1px solid transparent;
    &:hover {
      background-color: var(--color-primary-hover);
    }
    &:active {
      border: 1px solid var(--color-secondary-darker);
    }
  }
  &__secondary {
    background-color: var(--color-stroke);
    border: 1px solid transparent;
    color: var(--color-text);
    &:hover {
      background-color: var(--color-stroke-hover);
    }
    &:active {
      border: 1px solid var(--color-extreme-2);
    }
  }
  &__elevated {
    transform: translateY(-2px);
    box-shadow: var(--shadow-input);
    transition: all 0.2s;
    &:active {
      transform: translateY(0);
      box-shadow: var(--shadow-input-active);
      border: 1px solid transparent;
    }
  }
  &__progress {
    &:disabled {
      cursor: progress;
    }
  }
}

.scroll-bottom-available {
  &.scroll-top-available {
    mask-image: linear-gradient(
        to top,
        rgba(0, 0, 0, 1) 98%,
        rgba(0, 0, 0, 0) 100%
      ),
      linear-gradient(to bottom, rgba(0, 0, 0, 1) 98%, rgba(0, 0, 0, 0) 100%);
    mask-composite: intersect;
  }
  &:not(.scroll-top-available) {
    mask-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 1) 95%,
      rgba(0, 0, 0, 0) 100%
    );
    mask-composite: intersect;
  }
}
.scroll-top-available {
  &:not(.scroll-bottom-available) {
    mask-image: linear-gradient(
      to top,
      rgba(0, 0, 0, 1) 95%,
      rgba(0, 0, 0, 0) 100%
    );
    mask-composite: intersect;
  }
}

.over-scroll-top {
  & > * {
    animation: move-up 0.3s ease-in !important;
  }
}
.over-scroll-bottom {
  & > * {
    animation: move-down 0.3s ease-out !important;
  }
}
.no-scroll-bar {
  &::-webkit-scrollbar {
    display: none;
  }
}
.scroll-into-view {
  animation-name: appear;
  animation-timeline: view();
  animation-range: entry 0% cover 20%;
  animation-timing-function: ease-out;
}

//Check Box Card styles

.checkbox-card {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-stroke);
  border-radius: 0.5rem;
  cursor: pointer;
  flex: 1;
  .check-box {
    max-width: 2.5rem;
  }
  &:hover {
    background-color: var(--color-secondary);
  }
  &__title {
    font-weight: 500;
    color: var(--color-text);
  }
  &__description {
    font-size: 1rem;
    color: var(--color-text-secondary);
  }
}

//Shrink container
.shrink-container {
  display: grid !important;
  grid-template-rows: 1fr;
  overflow: hidden;
  transition: all 0.2s;
  &__content {
    overflow: hidden;
  }
  &--shrink {
    grid-template-rows: 0fr;
  }
}

//animation
@keyframes appear {
  from {
    opacity: 0;
    clip-path: inset(100% 100% 0 0);
  }
  to {
    opacity: 1;
    clip-path: inset(0 0 0 0);
  }
}

@keyframes move-up {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes move-down {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(0);
  }
}
