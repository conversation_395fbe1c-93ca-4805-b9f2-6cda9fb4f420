.container {
  flex: 1;
  display: flex;
  background-color: var(--color-background);
  overflow: auto;
  &__left,
  &__right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__left {
    background-color: var(--color-extreme-2);
    border-radius: 0 4rem 4rem 0;
    @media (max-width: 768px) {
      border-radius: 2rem;
      background-color: var(--color-background);
      margin: 2rem 3rem;
    }
  }
  &__right {
    @media (max-width: 768px) {
      display: none;
    }
  }
  &__content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
  }
  &__main {
    flex: 1;
    margin: 0 2rem;
    overflow: auto;
    display: flex;
  }
  &__toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
  }
}
