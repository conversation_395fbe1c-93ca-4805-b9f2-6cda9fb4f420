export const theme1 = {
  palette: {
    mode: "light",
    primary: {
      main: "#502799",
    },
    secondary: {
      main: "#ede8ff",
    },
    error: {
      main: "#dc3545",
    },
    warning: {
      main: "#ffc107",
    },
    info: {
      main: "#4a90e2",
    },
    success: {
      main: "#28a745",
    },
    background: {
      default: "#fff",
      paper: "#ffffff",
    },
    text: {
      primary: "#2a2d33",
      secondary: "#666666",
    },
    divider: "#e0e0e0",
    action: {
      active: "#502799",
      hover: "#f4f6f8",
      selected: "#f4f6f8",
      disabled: "#cccccc",
      disabledBackground: "#e0e0e0",
    },
    grey: {
      50: "#fafafa",
      100: "#f5f5f5",
      200: "#eeeeee",
      300: "#e0e0e0",
      400: "#bdbdbd",
      500: "#9e9e9e",
      600: "#757575",
      700: "#616161",
      800: "#424242",
      900: "#212121",
    },
  },
  typography: {
    fontFamily: ["Poppins", "sans-serif"].join(","),
  },
  components: {
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: "#33333390",
          color: "#ffffff",
        },
        arrow: {
          color: "#333333",
        },
      },
    },
    MuiSkeleton: {
      styleOverrides: {
        root: {
          backgroundColor: "#ede8ff",
        },
      },
    },
  },

  contrastThreshold: 3,
  tonalOffset: 0.2,
};

export const theme2 = {
  palette: {
    mode: "dark",
    primary: {
      main: "#823cff",
    },
    secondary: {
      main: "#382a49",
    },
    error: {
      main: "#ff6f6f",
    },
    warning: {
      main: "#ffc94b",
    },
    info: {
      main: "#105db7",
    },
    success: {
      main: "#37d67a",
    },
    background: {
      default: "#2a292b",
      paper: "#2a292b",
    },
    text: {
      primary: "#ffffff",
      secondary: "#83899f",
    },
    action: {
      active: "#823cff",
      hover: "#2e2e2e",
      selected: "#3a3a3a",
      disabled: "#555555",
      disabledBackground: "#1f1f1f",
    },
  },
  typography: {
    fontFamily: ["Poppins", "sans-serif"].join(","),
  },
  components: {
    MuiSkeleton: {
      styleOverrides: {
        root: {
          backgroundColor: "#382a49",
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          "&.Mui-selected": {
            backgroundColor: "#382a49",
          },
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        paper: {
          backgroundColor: "#1a1a1a", // dark background for Menu
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          backgroundColor: "#1a1a1a",
          "&:hover": {
            backgroundColor: "#2a2a2a",
          },
          "&.Mui-selected": {
            backgroundColor: "#2e2e2e",
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          backgroundColor: "#1a1a1a", // base row background
          "&:hover": {
            backgroundColor: "#2a2a2a", // hover effect
          },
          "&.Mui-selected": {
            backgroundColor: "#333333", // selected row
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: "#1a1a1a", // dark mode example
          color: "#fff", // optional: text color
        },
      },
    },
  },

  contrastThreshold: 3,
  tonalOffset: 0.2,
};
