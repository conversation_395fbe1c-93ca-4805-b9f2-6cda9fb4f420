const regexInputTypes = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^\+?[1-9]\d{8,10}$/,
  password: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  url: /^https?:\/\/[^\s$.?#].[^\s]*$/,
  date: /^\d{4}-\d{2}-\d{2}$/,
  creditCard: /^\d{16}$/,
  // postalCode: /^\d{5}(-\d{4})?$/,
  postalCode: /^\d{6}$/,
  ssn: /^\d{3}-\d{2}-\d{4}$/,
  username: /^[a-zA-Z0-9._-]{3,15}$/,
  ipv4: /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/,
  ipv6: /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
  companyName: /^[a-zA-Z0-9 @-_&]{1,50}$/,
  address:/^[a-zA-Z0-9\s,/-]+$/,
  integerField:/^\d+$/,
};

export default regexInputTypes;
