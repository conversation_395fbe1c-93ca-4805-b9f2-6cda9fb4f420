import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import TableActionField from "../../components/shared/Components/TableActionField/TableActionField";
import { useSelector } from "react-redux";

const useTableColumns = () => {
  const navigate = useNavigate();
  const fields = useSelector((state) => state.formConfigure.referenceFields);
  const userFields = fields?.data?.user_fields;
  const leadFields = fields?.data?.lead_fields;
  console.log(userFields);
  const companyTableColumns = useMemo(
    () => [
      {
        accessorKey: "company_id",
        header: "Company Id",
        enableHiding: false,
      },
      {
        accessorKey: "company_name",
        header: "Company Name",
      },
      {
        accessorKey: "company_website",
        header: "Company Website",
      },
      {
        accessorKey: "company_email",
        header: "Admin Email",
      },
      {
        accessorKey: "industry_type",
        header: "Type of Company",
      },
      {
        accessorKey: "employees_count",
        header: "Employees Count",
      },
      {
        accessorKey: "revenue",
        header: "Revenue",
      },
      {
        accessorKey: "phone",
        header: "Phone",
      },
      {
        accessorFn: (row) =>
          Object.values(row.address || {})
            .filter(Boolean)
            .join(", "),
        header: "Address",
      },
      {
        accessorKey: "users_limit",
        header: "Users Limit",
      },
      {
        accessorKey: "created_at",
        header: "Created At",
      },
      {
        accessorKey: "updated_at",
        header: "Updated At",
      },
      {
        accessorKey: "admin_name",
        header: "Admin Name",
      },
      {
        id: "mrt-row-actions",
        header: "Actions",
        enableHiding: false,
        enablePinning: false,
        enableColumnOrdering: false,
        size: 150,
        Cell: ({ row }) => (
          <TableActionField
            view
            close
            handleView={() => {
              navigate(`profile/${row.original.company_id}`, {
                state: { company: row.original },
              });
            }}
          />
        ),
      },
    ],
    [navigate]
  );

  const RolesTableColumns = useMemo(
    () => [
      {
        accessorKey: "role_id",
        header: "Role Id",
        enableHiding: false,
      },
      {
        accessorKey: "role_name",
        header: "Role Name",
      },
      {
        accessorKey: "role_description",
        header: "Role Description",
      },
      {
        accessorKey: "created_at",
        header: "Created At",
      },
      {
        accessorKey: "created_by",
        header: "Created By",
      },
      {
        accessorKey: "modified_at",
        header: "Modified At",
      },
      {
        accessorKey: "modified_by",
        header: "Modified By",
      },
    ],
    [navigate]
  );

  const UsersTableColumns = useMemo(
    () =>
      userFields?.flatMap((section) =>
        section?.fields?.map((field) => ({
          accessorKey: field.id,
          header: field.label,
        }))
      ),
    [navigate, userFields]
  );

  const LeadsTableColumns = useMemo(() => {
    const columns = leadFields?.flatMap((section) =>
      section?.fields?.map((field) => ({
        accessorKey: field.id,
        header: field.label,
      }))
    );
    const columnsWithActions = [
      ...(columns ?? []),
      {
        id: "mrt-row-actions",
        header: "Actions",
        enableHiding: false,
        enablePinning: false,
        enableColumnOrdering: false,
        size: 150,
        Cell: ({ row }) => (
          <TableActionField
            view
            close
            handleView={() => {
              navigate(`/leads/lead-details`);
            }}
          />
        ),
      },
    ];
    return columnsWithActions;
  }, [navigate, leadFields]);

  return {
    companyTableColumns,
    RolesTableColumns,
    UsersTableColumns,
    LeadsTableColumns,
  };
};

export default useTableColumns;
