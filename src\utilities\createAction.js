// actions.js
import { createAsyncThunk } from "@reduxjs/toolkit";
import { apiCall } from "./api_index";
import { baseUrl } from "./config";

export const createAsyncAction = (
  actionName,
  method,
  apiEndpoint,
  contentType = "application/json"
) => {
  const token = localStorage.getItem("token");

  return createAsyncThunk(actionName, async (payloadData, { dispatch }) => {
    try {
      const apiRequest = {
        method,
        url: `${baseUrl}${apiEndpoint}`,
        headers: token
          ? {
              "Content-Type": contentType,
              Authorization: "Token " + token,
            }
          : {
              "Content-Type": contentType,
            },
        data: payloadData.data,
      };
      const res = await apiCall(apiRequest, dispatch);
      return res.data;
    } catch (error) {
      throw error;
    }
  });
};
