import { createSlice } from "@reduxjs/toolkit";

const createAsyncSlice = (name, asyncAction) => {
  const initialState = {
    data: [],
    isLoading: false,
    error: null,
  };

  const slice = createSlice({
    name,
    initialState,
    reducers: {},
    extraReducers: (builder) => {
      builder
        .addCase(asyncAction.pending, (state) => {
          state.isLoading = true;
          state.error = null; // Reset error state
        })
        .addCase(asyncAction.fulfilled, (state, action) => {
          state.isLoading = false;
          if (action.payload.statusFlag === true) {
            state.data = action.payload.data;
          } else {
            state.error = action.payload.message;
          }
        })
        .addCase(asyncAction.rejected, (state, action) => {
          state.isLoading = false;
          state.error = action.error.message;
        });
    },
  });

  return slice;
};

export default createAsyncSlice;
