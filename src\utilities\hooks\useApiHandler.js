import { useCallback } from "react";
import { useDispatch } from "react-redux";

const useApiHandler = () => {
  const dispatch = useDispatch();

  const callApi = useCallback(
    (action, data, onSuccess, onFailure) => {
      const payload = { data };

      dispatch(action(payload))
        .unwrap()
        .then((response) => {
          if (response.statusFlag && typeof onSuccess === "function") {
            onSuccess(response);
          } else if (!response.statusFlag && typeof onFailure === "function") {
            onFailure(response.message);
          }
        })
        .catch((error) => {
          if (typeof onFailure === "function") {
            onFailure(error.message);
          }
        });
    },
    [dispatch]
  );

  return callApi;
};

export default useApiHandler;
