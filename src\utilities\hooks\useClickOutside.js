import { useEffect } from "react";

const useClickOutside = (refs, callback, shouldListen = true) => {
  useEffect(() => {
    if (!shouldListen) return;

    const handleClickOutside = (event) => {
      if (!refs) return;
      const isOutside = refs.every(
        (ref) => ref.current && !ref.current.contains(event.target)
      );
      if (isOutside) {
        callback?.();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("touchstart", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
    };
  }, [refs, callback, shouldListen]);
};

export default useClickOutside;
