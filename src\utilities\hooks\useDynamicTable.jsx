import { useEffect, useRef } from "react";
import { useMaterialReactTable } from "material-react-table";
import useTableQueryParams from "./useTableQueryParams";
import usePaginatedData from "./usePaginatedData";
import ImportSvg from "../../components/shared/importsvg/ImportSvg";

const useDynamicTable = ({
  columns,
  tableData,
  loading,
  setFormData,
  errorOccured,
  scrollRef,
}) => {
  const {
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,
  } = useTableQueryParams();
  const { flatData, dataAvailable } = usePaginatedData(
    tableData?.response_data,
    loading,
    setFormData,
    scrollRef,
    tableData?.next_page,
    errorOccured
  );

  const table = useMaterialReactTable({
    columns,
    data: flatData,
    manualSorting: true,
    manualFiltering: true,
    enableGlobalFilter: true,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    enablePagination: false,
    enableColumnResizing: true,
    enableStickyHeader: true,
    enableColumnOrdering: true,
    enableColumnPinning: true,
    initialState: {
      columnPinning: {
        right: ["mrt-row-actions"],
      },
    },
    state: {
      sorting,
      columnFilters,
      globalFilter,
      isLoading: loading && !dataAvailable,
      showProgressBars: dataAvailable && loading,
    },
    muiToolbarAlertBannerProps: errorOccured
      ? {
          color: "error",
          children: "Error loading data",
        }
      : undefined,
    muiTableHeadCellProps: {
      sx: {
        fontSize: "1.4rem",
        fontWeight: "bold",
        padding: "1rem",
      },
    },
    muiTableContainerProps: {
      sx: {
        fontSize: "1.2rem",
        height: "100%",
      },
      ref: scrollRef,
    },
    muiTableHeadRowProps: ({ table }) => ({
      sx: {
        marginBottom:
          table?.getState().density === "compact"
            ? "0.5rem"
            : table?.getState().density === "comfortable"
            ? "0.75rem"
            : "1rem",
      },
    }),
    muiTableBodyRowProps: ({ table }) => ({
      sx: {
        marginBottom:
          table?.getState().density === "compact"
            ? "0.25rem"
            : table?.getState().density === "comfortable"
            ? "0.4rem"
            : "0.5rem",
      },
    }),

    muiTableBodyCellProps: ({ table }) => ({
      sx: {
        paddlingLeft: "2rem",
        fontSize:
          table?.getState().density === "compact"
            ? "1.2rem"
            : table?.getState().density === "comfortable"
            ? "1.2rem"
            : "1.4rem",
      },
    }),

    icons: {
      MoreVertIcon: (props) => (
        <ImportSvg
          {...props}
          className="table-icon__large"
          icon="expand-more"
        />
      ),
      DragHandleIcon: (props) => (
        <ImportSvg
          {...props}
          className="table-icon__draggable"
          icon="draggable-icon"
        />
      ),
    },
  });

  return { table, flatData };
};

export default useDynamicTable;
