import { useState } from "react";

const useFormData = (initialFormData) => {
  const [formData, setFormData] = useState(initialFormData);

  const handleChange = (e) => {
    const { id, value } = e.target;

    setFormData((prevFormData) => {
      // Deep clone the entire formData to avoid mutating the original object
      const updatedFormData = JSON.parse(JSON.stringify(prevFormData));

      // Function to update the nested field
      const updateNestedField = (obj, path, value) => {
        const keys = path.split(".");
        const lastKey = keys.pop();
        const deepObj = keys.reduce((acc, key) => {
          if (!acc[key]) acc[key] = {}; // Ensure intermediate keys exist
          return acc[key];
        }, obj);
        deepObj[lastKey] = value; // Update the value
      };

      updateNestedField(updatedFormData, id, value);

      return updatedFormData;
    });
  };
  const handleCheckboxChange = (e) => {
    const { id, checked } = e.target;
    console.log(checked);
    setFormData((prevFormData) => {
      // Deep clone to avoid mutation
      const updatedFormData = JSON.parse(JSON.stringify(prevFormData));

      // Function to update nested field
      const updateNestedField = (obj, path, value) => {
        const keys = path.split(".");
        const lastKey = keys.pop();
        const deepObj = keys.reduce((acc, key) => {
          if (!acc[key]) acc[key] = {};
          return acc[key];
        }, obj);
        deepObj[lastKey] = value;
      };

      updateNestedField(updatedFormData, id, checked);

      return updatedFormData;
    });
  };

  return {
    formData,
    handleChange,
    handleCheckboxChange,
    setFormData,
  };
};

export default useFormData;
