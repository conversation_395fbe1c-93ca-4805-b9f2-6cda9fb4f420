import React, { useState, useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import useDebounce from "./useDebounce";

const usePaginatedData = (
  initialData,
  loading,
  setFormData,
  targetRef,
  nextPage,
  errorOccured
) => {
  const [prevPagesData, setPrevPagesData] = useState([]);
  const [dataAvailable, setDataAvailable] = useState(false);
  const [flatData, setFlatData] = useState([]);
  React.useEffect(() => {
    console.log(flatData, "flatData", initialData, "initialData");
  }, [initialData, flatData]);
  const location = useLocation();
  const isFetching = useRef(false);
  const handleScroll = () => {
    if (
      !targetRef?.current ||
      loading ||
      (!nextPage && !errorOccured) ||
      isFetching.current
    )
      return;

    const { scrollHeight, scrollTop, clientHeight } = targetRef.current;
    const shouldFetch = scrollHeight - scrollTop - clientHeight < 50;
    const noScrollAvailable = scrollHeight === clientHeight;
    const fetchData = () => {
      setDataAvailable(true);
      if (!loading) {
        isFetching.current = true;
        setFormData((prev) => ({
          ...prev,
          offset: prev.offset + prev.limit,
        }));
        setPrevPagesData((prev) => [...prev, ...(initialData || [])]);

        setTimeout(() => {
          isFetching.current = false;
        }, 500);
      }
    };

    if (shouldFetch && !noScrollAvailable) {
      fetchData();
    } else if (noScrollAvailable) {
      setTimeout(fetchData, 500);
    }
  };
  const debouncedHandleScroll = useDebounce(handleScroll, 300);
  useEffect(() => {
    if (errorOccured === false) {
      setFlatData(() => {
        const combinedData = [
          ...prevPagesData,
          ...(Array.isArray(initialData) ? initialData : []),
        ];

        const uniqueData = combinedData.filter(
          (item, index, self) => index === self.findIndex((t) => t === item)
        );

        return uniqueData;
      });
    }
    const element = targetRef.current;
    if (element) {
      element.addEventListener("scroll", debouncedHandleScroll);
      if (element.scrollHeight === element.clientHeight && !loading) {
        debouncedHandleScroll();
      }
    }

    return () => {
      if (element) {
        element.removeEventListener("scroll", handleScroll);
      }
    };
  }, [initialData, loading, nextPage, targetRef, setFormData, location.search]);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const paramsObject = Object.fromEntries(queryParams.entries());

    const hasOnlyViewParam =
      Object.keys(paramsObject).length === 1 &&
      paramsObject.hasOwnProperty("view");

    if (!hasOnlyViewParam) {
      setPrevPagesData([]);
      setDataAvailable(false);
    }
  }, [location.search]);

  return { flatData, dataAvailable };
};

export default usePaginatedData;
