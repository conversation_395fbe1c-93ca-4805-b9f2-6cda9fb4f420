import { useEffect } from "react";

const useScrollClasses = (ref, nextPage) => {
  useEffect(() => {
    const element = ref?.current;
    if (!element) return;

    let isOverScrollingTop = false;
    let isOverScrollingBottom = false;

    const updateScrollClasses = () => {
      if (!element) return;

      const { scrollTop, clientHeight, scrollHeight } = element;
      if (scrollTop > 0) {
        element.classList.add("scroll-top-available");
      } else {
        element.classList.remove("scroll-top-available");
      }

      if (scrollTop + clientHeight < scrollHeight - 1) {
        element.classList.add("scroll-bottom-available");
      } else {
        element.classList.remove("scroll-bottom-available");
      }
    };

    const handleOverScroll = (event) => {
      const { scrollTop, clientHeight, scrollHeight } = element;
      // Detect over-scrolling at the top
      if (scrollTop === 0) {
        if (!isOverScrollingTop) {
          element.classList.add("over-scroll-top");
          isOverScrollingTop = true;
        }
      } else {
        element.classList.remove("over-scroll-top");
        isOverScrollingTop = false;
      }

      // Detect over-scrolling at the bottom
      if (scrollTop + clientHeight > scrollHeight - 1) {
        if (!isOverScrollingBottom && !nextPage) {
          element.classList.add("over-scroll-bottom");
          isOverScrollingBottom = true;
        }
      } else {
        element.classList.remove("over-scroll-bottom");
        isOverScrollingBottom = false;
      }
    };

    // Perform initial check for scroll classes (this ensures they are applied even without scroll)
    updateScrollClasses();

    // Add scroll event listeners
    element.addEventListener("scroll", updateScrollClasses);
    element.addEventListener("scroll", handleOverScroll);

    // Clean up on unmount
    return () => {
      element.removeEventListener("scroll", updateScrollClasses);
      element.removeEventListener("scroll", handleOverScroll);
    };
  }); // ref as dependency to update when it changes
};

export default useScrollClasses;
