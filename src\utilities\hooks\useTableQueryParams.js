import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

const useTableQueryParams = (
  initialSorting = [],
  initialFilters = [],
  initialGlobalFilter = ""
) => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const initialSortingState = queryParams.get("order_parameter")
    ? [
        {
          id: queryParams.get("order_parameter"),
          desc: queryParams.get("order_by") === "desc",
        },
      ]
    : initialSorting;
  const initialFiltersState = queryParams.get("search_json")
    ? JSON.parse(decodeURIComponent(queryParams.get("search_json")))
    : initialFilters;

  const initialGlobalFilterState =
    queryParams.get("global_search") || initialGlobalFilter;

  const [sorting, setSorting] = useState(initialSortingState);
  const [columnFilters, setColumnFilters] = useState(initialFiltersState);
  const [globalFilter, setGlobalFilter] = useState(initialGlobalFilterState);

  const updateQueryParams = (key, value) => {
    const queryParams = new URLSearchParams(window.location.search);

    if (value) {
      queryParams.set(key, value);
    } else {
      queryParams.delete(key);
    }

    navigate(`${window.location.pathname}?${queryParams.toString()}`);
  };

  // Sync sorting with URL
  useEffect(() => {
    if (sorting.length > 0) {
      const { id, desc } = sorting[0] || {};
      updateQueryParams("order_parameter", id);
      updateQueryParams("order_by", desc ? "desc" : "asc");
    } else {
      updateQueryParams("order_parameter", null);
      updateQueryParams("order_by", null);
    }
  }, [sorting]);

  // Sync column filters with URL
  useEffect(() => {
    if (columnFilters.length > 0) {
      const filtersJson = JSON.stringify(columnFilters);
      updateQueryParams("search_json", encodeURIComponent(filtersJson));
    } else {
      updateQueryParams("search_json", null);
    }
  }, [columnFilters]);

  // Sync global search filter with URL
  useEffect(() => {
    updateQueryParams("global_search", globalFilter?.trim() || null);
  }, [globalFilter]);

  return {
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,
  };
};

export default useTableQueryParams;
