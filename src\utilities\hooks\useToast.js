import { useDispatch } from "react-redux";
import { addToast, removeToast } from "../../store/slice/generalSlice";

export const useToast = () => {
  const dispatch = useDispatch();
  const showToast = (title, message, type = "success", duration = 6000) => {
    const id = Date.now();
    dispatch(addToast({ id, title, message, type }));

    setTimeout(() => {
      dispatch(removeToast(id));
    }, duration);
  };

  return showToast;
};
