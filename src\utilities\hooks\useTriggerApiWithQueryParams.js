import { useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import useApi<PERSON>andler from "./useApiHandler";

const useTriggerApiWithQueryParams = (
  action,
  formData,
  setFormData,
  loading
) => {
  const [errorOccured, setErrorOccured] = useState(false);
  const location = useLocation();
  const callApi = useApiHandler();
  const hasInitialized = useRef(false);
  const prevQueryParamsRef = useRef(null);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const updatedFormData = {};

    if (location.search) {
      queryParams.forEach((value, key) => {
        try {
          updatedFormData[key] = decodeURIComponent(value);
        } catch (error) {
          console.warn(`Error parsing query parameter "${key}":`, error);
          updatedFormData[key] = value;
        }
      });
    }

    const prevQueryParams = prevQueryParamsRef.current;
    prevQueryParamsRef.current = Object.fromEntries(queryParams.entries());

    setFormData((prevData) => {
      const paramsObject = Object.fromEntries(queryParams.entries());
      const hasOnlyViewParam =
        Object.keys(paramsObject).length === 1 &&
        paramsObject.hasOwnProperty("view");

      const hadMultipleParamsBefore =
        prevQueryParams && Object.keys(prevQueryParams).length > 1;

      console.warn("Prev:", prevQueryParams, "Current:", paramsObject);

      if (hadMultipleParamsBefore && hasOnlyViewParam) {
        return {
          ...prevData,
          order_parameter: "",
          global_search: "",
          order_by: "",
          search_parameter: "",
          search_value: "",
          search_json: [],
          offset: 0,
        };
      } else if (
        prevQueryParams &&
        hasOnlyViewParam &&
        Object.keys(prevQueryParams).length === 1
      ) {
        return prevData;
      }

      // Merge new query params while keeping existing data
      const newFormData = { ...prevData, ...updatedFormData, offset: 0 };

      return JSON.stringify(newFormData) !== JSON.stringify(prevData)
        ? newFormData
        : prevData;
    });
  }, [location.search, setFormData]);

  useEffect(() => {
    console.log("formData from useTriggerApiWithQueryParams", formData);
  }, [formData, location.search]);

  useEffect(() => {
    if (hasInitialized.current && formData && !loading) {
      callApi(
        action,
        formData,
        () => setErrorOccured(false),
        () => setErrorOccured(true)
      );
    } else {
      hasInitialized.current = true;
    }
  }, [formData, action, callApi]);

  return { errorOccured };
};

export default useTriggerApiWithQueryParams;
