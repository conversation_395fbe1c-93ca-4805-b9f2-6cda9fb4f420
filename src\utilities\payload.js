const user_id = localStorage.getItem("user_id") || "";

export const payload = {
  loginView: {
    user_email: "",
    password: "",
    login_platform: "web",
    ip_address: "",
  },
  verifyUUID: {
    user_email: "",
    UUID: "",
  },
  updatePassword: {
    user_email: "",
    password: "",
  },
  forgetPassword: { user_email: "" },
  companyRegister: {
    password: "",
    org_id: "",
    profile: null,
    country_code: "",
    company_name: "",
    company_website: "",
    privacy_policy: "",
    company_email: "",
    industry_type: "",
    employees_count: 0,
    revenue: "",
    phone: "",
    address: "",
    modules: {},
    other_details: {
      description: "",
      social_media: {
        facebook: "",
        instagram: "",
        whatsapp: "",
        linkedin: "",
        skype: "",
        x: "",
      },
    },
  },
  getCompanyDetails: { user_id, company_id: "", offset: 0, limit: 100 },
  emailValidate: {
    user_email: "",
    user_type: "",
    company_name: "",
    admin_name: "",
    modules: "",
    users_limit: 0,
    user_id,
  },
  getOrderedCompanyDetails: {
    user_id,
    org_id: user_id,
    global_search: "",
    search_json: [],
    company_id: "",
    order_parameter: "",
    order_by: "",
    search_parameter: "",
    search_value: "",
    limit: 15,
    offset: 0,
  },
  createReferenceFields: {
    user_id,
    field_type: "",
  },
  createRole: {
    user_id: "USR_00000002",
    role_name: "",
    role_description: "",
    access: "",
  },
  editRole: {
    user_id: "USR_00000002",
    role_id: "",
    role_name: "",
    role_description: "",
    access: "",
    modified_at: "",
  },
  userRegister: {
    user_id: "USR_00000002",
    user_fields: [],
  },
  allCompanyUsers: {
    user_id: "USR_00000002",
    global_search: "",
    search_json: [],
    order_parameter: "",
    order_by: "",
    search_parameter: "",
    search_value: "",
    limit: 15,
    offset: 0,
  },
  leadCreation: {
    user_id: "USR_00000002",
    lead_fields: [],
  },
  leadsList: {
    user_id: "USR_00000002",
    global_search: "",
    search_json: [],
    order_parameter: "",
    order_by: "",
    search_parameter: "",
    search_value: "",
    limit: 15,
    offset: 0,
  },
};
